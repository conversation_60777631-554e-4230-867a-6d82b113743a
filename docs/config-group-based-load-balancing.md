# 基于配置分组的智能负载均衡方案

## 🎯 优化目标

将原有的基于提供商的API密钥选择逻辑优化为基于用户配置分组的选择逻辑，充分利用重构后的分组配置系统，实现更精细化的负载均衡管理。

## 🔄 新的选择流程

### 原有流程
```
兼容APIkey → 用户ID → 模型名称 → 提供商类型 → 提供商下所有API密钥 → 负载均衡选择
```

### 优化后流程
```
兼容APIkey → 用户ID → 模型名称 → 提供商类型 → 匹配的配置分组 → 分组内API密钥 → 负载均衡选择
```

## 🏗️ 核心实现

### 1. 新的选择方法

```java
/**
 * 基于配置分组选择API密钥
 * 新的选择逻辑：用户ID → 模型名称 → 提供商类型 → 匹配的配置分组 → 分组内API密钥负载均衡
 */
private ApiKeySelectionResult selectApiKeyFromConfigGroup(Long userId, String model) {
    // 第一步：根据模型确定提供商类型
    UserApiKey.ProviderType provider = determineProviderByModel(model);
    
    // 第二步：获取用户所有配置分组
    List<UserAiConfig> userConfigs = aiConfigService.getUserConfigGroups(userId);
    
    // 第三步：查找匹配提供商的配置分组
    List<UserAiConfig> matchingConfigs = userConfigs.stream()
            .filter(config -> provider.name().equals(config.getProvider()))
            .collect(Collectors.toList());
    
    // 第四步：为每个匹配的配置分组选择最佳API密钥
    for (UserAiConfig config : matchingConfigs) {
        ApiKeySelectionResult result = selectBestApiKeyFromGroup(userId, config, model);
        if (result.isSuccess()) {
            return result;
        }
    }
}
```

### 2. 分组内负载均衡算法

```java
/**
 * 负载均衡算法选择最佳API密钥
 * 实现三层优先级的负载均衡算法：
 * 1. 当前请求数（最少连接优先）
 * 2. 错误率（错误率低的优先）  
 * 3. 总请求数（轮询效果）
 */
private UserApiKey selectBestApiKeyWithLoadBalance(List<UserApiKey> apiKeys) {
    UserApiKey bestKey = null;
    LoadBalanceStats bestStats = null;

    for (UserApiKey apiKey : apiKeys) {
        // 检查健康状态
        if (!loadBalancerService.isApiKeyHealthy(apiKey.getId())) {
            continue;
        }

        // 获取负载统计
        LoadBalanceStats stats = loadBalancerService.getLoadBalanceStats(apiKey.getId());
        
        // 应用三层优先级算法
        if (bestKey == null || isBetterChoice(stats, bestStats)) {
            bestKey = apiKey;
            bestStats = stats;
        }
    }

    return bestKey;
}
```

### 3. 三层优先级比较逻辑

```java
private boolean isBetterChoice(LoadBalanceStats candidate, LoadBalanceStats current) {
    // 第一优先级：当前请求数（最少连接优先）
    if (candidate.getCurrentRequests() < current.getCurrentRequests()) {
        return true;
    }
    if (candidate.getCurrentRequests() > current.getCurrentRequests()) {
        return false;
    }

    // 第二优先级：错误率
    double candidateErrorRate = calculateErrorRate(candidate);
    double currentErrorRate = calculateErrorRate(current);
    if (candidateErrorRate < currentErrorRate) {
        return true;
    }
    if (candidateErrorRate > currentErrorRate) {
        return false;
    }

    // 第三优先级：总请求数（轮询效果）
    return candidate.getTotalRequests() < current.getTotalRequests();
}
```

## 📊 数据库结构调整

### 1. api_key_load_balance表更新

```sql
-- 添加config_group_id字段，移除provider字段
ALTER TABLE api_key_load_balance 
ADD COLUMN config_group_id BIGINT NOT NULL AFTER user_id,
DROP COLUMN provider;

-- 更新索引
DROP INDEX idx_user_provider;
CREATE INDEX idx_user_group ON api_key_load_balance(user_id, config_group_id);
```

### 2. 新的负载均衡服务方法

```java
/**
 * 选择最佳的API密钥（基于配置分组）
 * 根据负载均衡算法从指定配置分组中选择当前最适合的API密钥
 */
UserApiKey selectBestApiKeyFromGroup(Long userId, Long configGroupId);
```

## 🚀 优势和特性

### 1. **精细化管理**
- ✅ 支持用户为不同用途创建不同的配置分组
- ✅ 每个分组可以有独立的API密钥池和配置
- ✅ 支持分组级别的负载均衡和故障转移

### 2. **灵活的配置策略**
- ✅ 用户可以为生产环境和测试环境创建不同分组
- ✅ 支持不同分组使用不同的自定义URL
- ✅ 每个分组可以配置独立的测试模型

### 3. **智能选择逻辑**
- ✅ 优先选择用户配置的分组
- ✅ 支持多个分组的故障转移
- ✅ 保持原有的负载均衡算法优势

### 4. **向后兼容性**
- ✅ 保留原有的基于提供商的选择方法
- ✅ 新老逻辑可以并存
- ✅ 渐进式迁移支持

## 🔧 使用场景示例

### 场景1：多环境管理
```
用户配置：
- 分组1: "生产环境" (provider: OPENAI, custom_base_url: https://api.openai.com/v1)
- 分组2: "测试环境" (provider: OPENAI, custom_base_url: https://test-api.openai.com/v1)

请求gpt-4模型时：
1. 识别提供商为OPENAI
2. 查找用户的OPENAI分组
3. 优先使用"生产环境"分组的API密钥
4. 如果生产环境密钥不可用，自动切换到"测试环境"
```

### 场景2：不同供应商管理
```
用户配置：
- 分组1: "OpenAI官方" (provider: OPENAI, custom_base_url: https://api.openai.com/v1)
- 分组2: "OpenAI代理" (provider: OPENAI, custom_base_url: https://proxy.example.com/v1)

请求处理：
1. 根据模型确定需要OPENAI提供商
2. 在两个OPENAI分组中进行负载均衡
3. 实现供应商级别的故障转移
```

## 📈 性能优化

### 1. **缓存策略**
- 配置分组信息缓存
- 负载统计信息缓存
- 减少数据库查询次数

### 2. **批量操作**
- 批量初始化负载状态
- 批量更新统计信息
- 优化数据库操作性能

### 3. **异步处理**
- 异步更新负载统计
- 异步健康检查
- 不阻塞主要业务流程

## 🔍 监控和统计

### 1. **分组级别统计**
- 每个配置分组的请求量统计
- 分组内API密钥的使用分布
- 分组级别的成功率和错误率

### 2. **智能告警**
- 分组内所有密钥不可用告警
- 分组级别的错误率告警
- 负载不均衡告警

## 🎯 总结

这个基于配置分组的智能负载均衡方案充分利用了重构后的分组配置系统，提供了更精细化、更灵活的API密钥管理和负载均衡能力。通过分组级别的管理，用户可以更好地组织和管理他们的API密钥资源，同时系统也能提供更智能的选择和故障转移机制。

**核心优势：**
- 🎯 **精细化管理**：分组级别的配置和负载均衡
- 🔄 **智能选择**：基于配置分组的多层选择逻辑  
- 📈 **性能优化**：减少无效查询，提高选择效率
- 🛡️ **故障转移**：分组级别的故障转移和恢复
- 🔧 **灵活配置**：支持多种使用场景和配置策略

编译状态：✅ 成功
实现状态：✅ 完成
