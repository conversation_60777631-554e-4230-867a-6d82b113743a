2025-08-01 17:26:17.331 [34mINFO [0;39m 15924 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 15924 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-08-01 17:26:17.335 [39mDEBUG[0;39m 15924 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-01 17:26:17.335 [34mINFO [0;39m 15924 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-08-01 17:26:17.342 [34mINFO [0;39m 15924 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-08-01 17:26:18.606 [34mINFO [0;39m 15924 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 17:26:18.612 [34mINFO [0;39m 15924 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 17:26:18.690 [34mINFO [0;39m 15924 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 59 ms. Found 0 Redis repository interfaces.
2025-08-01 17:26:18.873 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-08-01 17:26:18.873 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ApiKeyLoadBalanceMapper.class]
2025-08-01 17:26:18.873 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ChatSessionMapper.class]
2025-08-01 17:26:18.873 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\CompatibleApiKeyMapper.class]
2025-08-01 17:26:18.873 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-08-01 17:26:18.873 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-08-01 17:26:18.873 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-08-01 17:26:18.873 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-08-01 17:26:18.873 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-08-01 17:26:18.873 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-08-01 17:26:18.873 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-08-01 17:26:18.873 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-08-01 17:26:18.873 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-08-01 17:26:18.873 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserAiConfigMapper.class]
2025-08-01 17:26:18.874 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserApiKeyMapper.class]
2025-08-01 17:26:18.874 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-08-01 17:26:18.874 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-08-01 17:26:18.874 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-08-01 17:26:18.874 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-08-01 17:26:18.876 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-08-01 17:26:18.877 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-08-01 17:26:18.878 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'apiKeyLoadBalanceMapper' and 'com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper' mapperInterface
2025-08-01 17:26:18.878 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'apiKeyLoadBalanceMapper'.
2025-08-01 17:26:18.878 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'chatSessionMapper' and 'com.example.pure.mapper.primary.ChatSessionMapper' mapperInterface
2025-08-01 17:26:18.878 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'chatSessionMapper'.
2025-08-01 17:26:18.878 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'compatibleApiKeyMapper' and 'com.example.pure.mapper.primary.CompatibleApiKeyMapper' mapperInterface
2025-08-01 17:26:18.879 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'compatibleApiKeyMapper'.
2025-08-01 17:26:18.879 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-08-01 17:26:18.879 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-08-01 17:26:18.879 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-08-01 17:26:18.880 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-08-01 17:26:18.880 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-08-01 17:26:18.880 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-08-01 17:26:18.880 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-08-01 17:26:18.881 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-08-01 17:26:18.881 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-08-01 17:26:18.881 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-08-01 17:26:18.881 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-08-01 17:26:18.881 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-08-01 17:26:18.882 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-08-01 17:26:18.882 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-08-01 17:26:18.882 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-08-01 17:26:18.882 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-08-01 17:26:18.882 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-08-01 17:26:18.883 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-08-01 17:26:18.883 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userAiConfigMapper' and 'com.example.pure.mapper.primary.UserAiConfigMapper' mapperInterface
2025-08-01 17:26:18.883 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userAiConfigMapper'.
2025-08-01 17:26:18.883 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userApiKeyMapper' and 'com.example.pure.mapper.primary.UserApiKeyMapper' mapperInterface
2025-08-01 17:26:18.883 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userApiKeyMapper'.
2025-08-01 17:26:18.883 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-08-01 17:26:18.884 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-08-01 17:26:18.884 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-08-01 17:26:18.884 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-08-01 17:26:18.884 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-08-01 17:26:18.885 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-08-01 17:26:18.885 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-08-01 17:26:18.885 [39mDEBUG[0;39m 15924 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-08-01 17:26:19.620 [34mINFO [0;39m 15924 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-08-01 17:26:19.627 [34mINFO [0;39m 15924 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 17:26:19.629 [34mINFO [0;39m 15924 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-08-01 17:26:19.629 [34mINFO [0;39m 15924 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-01 17:26:19.738 [34mINFO [0;39m 15924 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-08-01 17:26:19.739 [34mINFO [0;39m 15924 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2321 ms
2025-08-01 17:26:20.102 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-08-01 17:26:20.113 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ApiKeyLoadBalanceMapper.xml]'
2025-08-01 17:26:20.124 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ChatSessionMapper.xml]'
2025-08-01 17:26:20.133 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\CompatibleApiKeyMapper.xml]'
2025-08-01 17:26:20.140 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\MessagesMapper.xml]'
2025-08-01 17:26:20.145 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-08-01 17:26:20.150 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-08-01 17:26:20.160 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-08-01 17:26:20.164 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-08-01 17:26:20.170 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-08-01 17:26:20.174 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-08-01 17:26:20.185 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-08-01 17:26:20.192 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\RoleMapper.xml]'
2025-08-01 17:26:20.200 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]'
2025-08-01 17:26:20.206 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]'
2025-08-01 17:26:20.213 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserMapper.xml]'
2025-08-01 17:26:20.218 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-08-01 17:26:20.224 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-08-01 17:26:20.229 [39mDEBUG[0;39m 15924 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-08-01 17:26:20.244 [34mINFO [0;39m 15924 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-08-01 17:26:20.610 [34mINFO [0;39m 15924 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-08-01 17:26:21.179 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-08-01 17:26:21.180 [39mDEBUG[0;39m 15924 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-08-01 17:26:21.510 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-01 17:26:21.513 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-01 17:26:22.144 [34mINFO [0;39m 15924 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-08-01 17:26:22.330 [34mINFO [0;39m 15924 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-08-01 17:26:22.338 [34mINFO [0;39m 15924 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-08-01 17:26:22.403 [34mINFO [0;39m 15924 --- [main] c.e.pure.config.SnowflakeConfig : 初始化雪花算法ID生成器 - 机器ID: 1, 数据中心ID: 1
2025-08-01 17:26:22.403 [34mINFO [0;39m 15924 --- [main] c.e.pure.util.SnowflakeIdGenerator : 雪花算法ID生成器初始化 - 机器ID: 1, 数据中心ID: 1
2025-08-01 17:26:22.415 [34mINFO [0;39m 15924 --- [main] c.e.pure.util.SpringEncryptionUtil : Spring加密工具初始化成功（CBC模式）
2025-08-01 17:26:22.438 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-08-01 17:26:22.439 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-08-01 17:26:22.440 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-08-01 17:26:22.440 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-01 17:26:22.440 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-08-01 17:26:22.440 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-01 17:26:22.440 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-01 17:26:22.440 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-01 17:26:22.440 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-08-01 17:26:22.441 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-01 17:26:22.441 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-08-01 17:26:22.441 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-01 17:26:22.445 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-08-01 17:26:22.445 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-01 17:26:22.446 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-08-01 17:26:22.446 [39mDEBUG[0;39m 15924 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-08-01 17:26:22.564 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-08-01 17:26:22.565 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-08-01 17:26:22.568 [34mINFO [0;39m 15924 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@17a89cb1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@68fec965, org.springframework.security.web.context.SecurityContextPersistenceFilter@6012bee8, org.springframework.security.web.header.HeaderWriterFilter@279e4506, org.springframework.security.web.authentication.logout.LogoutFilter@622e39d, com.example.pure.filter.JwtFilter@67b09e34, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@62d72091, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@76a86677, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7af5ce66, org.springframework.security.web.session.SessionManagementFilter@696d28ee, org.springframework.security.web.access.ExceptionTranslationFilter@58e9e852, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3dc5eb8f]
2025-08-01 17:26:22.573 [34mINFO [0;39m 15924 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-08-01 17:26:22.575 [34mINFO [0;39m 15924 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-08-01 17:26:22.575 [34mINFO [0;39m 15924 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-08-01 17:26:22.575 [34mINFO [0;39m 15924 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-08-01 17:26:22.751 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-08-01 17:26:22.770 [34mINFO [0;39m 15924 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-08-01 17:26:22.831 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 105 mappings in 'requestMappingHandlerMapping'
2025-08-01 17:26:22.838 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-08-01 17:26:23.187 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-08-01 17:26:23.305 [34mINFO [0;39m 15924 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-08-01 17:26:23.325 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-08-01 17:26:23.325 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-08-01 17:26:23.325 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-08-01 17:26:23.325 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-08-01 17:26:23.325 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-08-01 17:26:23.325 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-08-01 17:26:23.325 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v1/**']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-08-01 17:26:23.326 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-08-01 17:26:23.327 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-08-01 17:26:23.327 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-08-01 17:26:23.327 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-08-01 17:26:23.327 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-08-01 17:26:23.327 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-08-01 17:26:23.327 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-08-01 17:26:23.327 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-08-01 17:26:23.327 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-08-01 17:26:23.327 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-08-01 17:26:23.327 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-08-01 17:26:23.327 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/ai/config/**']
2025-08-01 17:26:23.327 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-08-01 17:26:23.327 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-08-01 17:26:23.327 [39mDEBUG[0;39m 15924 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-08-01 17:26:23.328 [34mINFO [0;39m 15924 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@371fdf43, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@37f687c1, org.springframework.security.web.context.SecurityContextPersistenceFilter@7d41aefd, org.springframework.security.web.header.HeaderWriterFilter@c509742, org.springframework.web.filter.CorsFilter@297da2c3, org.springframework.security.web.authentication.logout.LogoutFilter@35ffe3ac, com.example.pure.filter.JwtFilter@67b09e34, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@feb57b4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7893b8c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6237927a, org.springframework.security.web.session.SessionManagementFilter@77a6aa9f, org.springframework.security.web.access.ExceptionTranslationFilter@206a465f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6ea4b4b2]
2025-08-01 17:26:23.363 [39mTRACE[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@314b8f2d, started on Fri Aug 01 17:26:17 CST 2025
2025-08-01 17:26:23.377 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.AuthController:
	
2025-08-01 17:26:23.377 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OAuth2Controller:
	
2025-08-01 17:26:23.377 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OperatingLogController:
	
2025-08-01 17:26:23.377 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRCodeController:
	
2025-08-01 17:26:23.377 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRLoginController:
	
2025-08-01 17:26:23.377 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.VerificationController:
	
2025-08-01 17:26:23.377 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.s.R2Controller:
	
2025-08-01 17:26:23.377 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.DownloadController:
	
2025-08-01 17:26:23.377 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.FileManagerController:
	
2025-08-01 17:26:23.377 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.ImageController:
	
2025-08-01 17:26:23.377 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureFileManagerController:
	
2025-08-01 17:26:23.377 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureImageFileController:
	
2025-08-01 17:26:23.377 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.VideoController:
	
2025-08-01 17:26:23.378 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.m.MessagesController:
	
2025-08-01 17:26:23.378 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiChatController:
	
2025-08-01 17:26:23.378 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiConfigController:
	
2025-08-01 17:26:23.378 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiSystemTestController:
	
2025-08-01 17:26:23.378 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiCompatibleController:
	
2025-08-01 17:26:23.378 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserController:
	
2025-08-01 17:26:23.381 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.WebSocketTestController:
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/echo]}: echo(String)
2025-08-01 17:26:23.381 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserProfileController:
	
2025-08-01 17:26:23.381 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoInteractionController:
	
2025-08-01 17:26:23.381 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoUrlController:
	
2025-08-01 17:26:23.381 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.VideoCommentInteractionController:
	
2025-08-01 17:26:23.382 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-08-01 17:26:23.382 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-08-01 17:26:23.382 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-08-01 17:26:23.382 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-08-01 17:26:23.478 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-01 17:26:23.517 [39mDEBUG[0;39m 15924 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-01 17:26:23.760 [34mINFO [0;39m 15924 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 17:26:23.770 [34mINFO [0;39m 15924 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 17:26:23.771 [39mDEBUG[0;39m 15924 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-01 17:26:23.772 [39mDEBUG[0;39m 15924 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-01 17:26:23.772 [34mINFO [0;39m 15924 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-08-01 17:26:23.772 [39mDEBUG[0;39m 15924 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e7cdb2]
2025-08-01 17:26:23.772 [39mDEBUG[0;39m 15924 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e7cdb2]
2025-08-01 17:26:23.772 [34mINFO [0;39m 15924 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e7cdb2]]
2025-08-01 17:26:23.772 [34mINFO [0;39m 15924 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-08-01 17:26:23.772 [39mDEBUG[0;39m 15924 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-01 17:26:23.772 [39mDEBUG[0;39m 15924 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-01 17:26:23.784 [34mINFO [0;39m 15924 --- [main] com.example.pure.PureApplication : Started PureApplication in 7.514 seconds (JVM running for 9.444)
2025-08-01 17:27:23.349 [34mINFO [0;39m 15924 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-08-01 17:57:23.354 [34mINFO [0;39m 15924 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-08-01 18:04:27.498 [34mINFO [0;39m 15924 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 18:04:27.498 [34mINFO [0;39m 15924 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-08-01 18:04:27.498 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-08-01 18:04:27.498 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-08-01 18:04:27.498 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-08-01 18:04:27.501 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@514b7ff4
2025-08-01 18:04:27.502 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@511f1df2
2025-08-01 18:04:27.502 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-08-01 18:04:27.502 [34mINFO [0;39m 15924 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 4 ms
2025-08-01 18:04:27.514 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing PUT /api/ai/config/config-groups
2025-08-01 18:04:27.517 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-01 18:04:27.523 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#createOrUpdateUserConfigGroup(UserConfigGroupDto, Authentication)
2025-08-01 18:04:28.031 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-08-01 18:04:28.047 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:04:28.051 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1bc8d8df]
2025-08-01 18:04:28.057 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1612172183 wrapping com.mysql.cj.jdbc.ConnectionImpl@6c6dc90e] will be managed by Spring
2025-08-01 18:04:28.060 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-08-01 18:04:28.081 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: admin(String)
2025-08-01 18:04:28.109 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-08-01 18:04:28.110 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1bc8d8df]
2025-08-01 18:04:28.111 [34mINFO [0;39m 15924 --- [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-08-01 18:04:28.112 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1bc8d8df] from current transaction
2025-08-01 18:04:28.112 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-08-01 18:04:28.112 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-08-01 18:04:28.118 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-08-01 18:04:28.118 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1bc8d8df]
2025-08-01 18:04:28.119 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-08-01 18:04:28.120 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1bc8d8df]
2025-08-01 18:04:28.120 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1bc8d8df]
2025-08-01 18:04:28.120 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1bc8d8df]
2025-08-01 18:04:28.164 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-08-01 18:04:28.171 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [PUT /api/ai/config/config-groups] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-01 18:04:28.171 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured PUT /api/ai/config/config-groups
2025-08-01 18:04:28.173 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : PUT "/api/ai/config/config-groups", parameters={}
2025-08-01 18:04:28.175 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#createOrUpdateUserConfigGroup(UserConfigGroupDto, Authentication)
2025-08-01 18:04:28.210 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.web.method.HandlerMethod : Could not resolve parameter [0] in public com.example.pure.common.Result<com.example.pure.model.dto.response.openai.UserConfigGroupDto> com.example.pure.controller.openai.AiConfigController.createOrUpdateUserConfigGroup(com.example.pure.model.dto.response.openai.UserConfigGroupDto,org.springframework.security.core.Authentication): JSON parse error: Unrecognized field "apiKey" (class com.example.pure.model.dto.response.openai.UserConfigGroupDto), not marked as ignorable; nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "apiKey" (class com.example.pure.model.dto.response.openai.UserConfigGroupDto), not marked as ignorable (13 known properties: "streamEnabled", "preferredModel", "groupName", "customBaseUrl", "updatedAt", "provider", "defaultTemperature", "testModel", "createdAt", "defaultTopP", "defaultMaxTokens", "timeoutSeconds", "systemPrompt"])
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 6, column: 15] (through reference chain: com.example.pure.model.dto.response.openai.UserConfigGroupDto["apiKey"])
2025-08-01 18:04:28.211 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-08-01 18:04:28.213 [1;31mERROR[0;39m 15924 --- [http-nio-8080-exec-1] c.e.p.e.GlobalExceptionHandler : 服务器处理API出现异常
org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unrecognized field "apiKey" (class com.example.pure.model.dto.response.openai.UserConfigGroupDto), not marked as ignorable; nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "apiKey" (class com.example.pure.model.dto.response.openai.UserConfigGroupDto), not marked as ignorable (13 known properties: "streamEnabled", "preferredModel", "groupName", "customBaseUrl", "updatedAt", "provider", "defaultTemperature", "testModel", "createdAt", "defaultTopP", "defaultMaxTokens", "timeoutSeconds", "systemPrompt"])
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 6, column: 15] (through reference chain: com.example.pure.model.dto.response.openai.UserConfigGroupDto["apiKey"])
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:391)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.read(AbstractJackson2HttpMessageConverter.java:343)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodArgumentResolver.readWithMessageConverters(AbstractMessageConverterMethodArgumentResolver.java:185)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:160)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:133)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:179)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:146)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:558)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "apiKey" (class com.example.pure.model.dto.response.openai.UserConfigGroupDto), not marked as ignorable (13 known properties: "streamEnabled", "preferredModel", "groupName", "customBaseUrl", "updatedAt", "provider", "defaultTemperature", "testModel", "createdAt", "defaultTopP", "defaultMaxTokens", "timeoutSeconds", "systemPrompt"])
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 6, column: 15] (through reference chain: com.example.pure.model.dto.response.openai.UserConfigGroupDto["apiKey"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:1127)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:2036)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1700)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1678)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:320)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3682)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:380)
	... 105 common frames omitted
2025-08-01 18:04:28.222 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-08-01 18:04:28.227 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [Result(code=500, message=服务器处理API出现异常，请联系管理员, success=false, data=null, time=2025-08-01T10:04:28.216 (truncated)...]
2025-08-01 18:04:28.236 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unrecognized field "apiKey" (class com.example.pure.model.dto.response.openai.UserConfigGroupDto), not marked as ignorable; nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "apiKey" (class com.example.pure.model.dto.response.openai.UserConfigGroupDto), not marked as ignorable (13 known properties: "streamEnabled", "preferredModel", "groupName", "customBaseUrl", "updatedAt", "provider", "defaultTemperature", "testModel", "createdAt", "defaultTopP", "defaultMaxTokens", "timeoutSeconds", "systemPrompt"])<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 6, column: 15] (through reference chain: com.example.pure.model.dto.response.openai.UserConfigGroupDto["apiKey"])]
2025-08-01 18:04:28.237 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 500 INTERNAL_SERVER_ERROR
2025-08-01 18:04:28.237 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-01 18:04:49.761 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing PUT /api/ai/config/config-groups
2025-08-01 18:04:49.762 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-01 18:04:49.762 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#createOrUpdateUserConfigGroup(UserConfigGroupDto, Authentication)
2025-08-01 18:04:49.847 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-08-01 18:04:49.857 [34mINFO [0;39m 15924 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-08-01 18:04:49.857 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:04:49.857 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c4fa4de]
2025-08-01 18:04:49.858 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@102171507 wrapping com.mysql.cj.jdbc.ConnectionImpl@6c6dc90e] will be managed by Spring
2025-08-01 18:04:49.858 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-08-01 18:04:49.858 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-08-01 18:04:49.860 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-08-01 18:04:49.860 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c4fa4de]
2025-08-01 18:04:49.860 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-08-01 18:04:49.860 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c4fa4de]
2025-08-01 18:04:49.860 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c4fa4de]
2025-08-01 18:04:49.860 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c4fa4de]
2025-08-01 18:04:49.862 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-08-01 18:04:49.863 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [PUT /api/ai/config/config-groups] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-01 18:04:49.863 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured PUT /api/ai/config/config-groups
2025-08-01 18:04:49.863 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : PUT "/api/ai/config/config-groups", parameters={}
2025-08-01 18:04:49.864 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#createOrUpdateUserConfigGroup(UserConfigGroupDto, Authentication)
2025-08-01 18:04:49.865 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] o.s.web.method.HandlerMethod : Could not resolve parameter [0] in public com.example.pure.common.Result<com.example.pure.model.dto.response.openai.UserConfigGroupDto> com.example.pure.controller.openai.AiConfigController.createOrUpdateUserConfigGroup(com.example.pure.model.dto.response.openai.UserConfigGroupDto,org.springframework.security.core.Authentication): JSON parse error: Unrecognized field "isActive" (class com.example.pure.model.dto.response.openai.UserConfigGroupDto), not marked as ignorable; nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "isActive" (class com.example.pure.model.dto.response.openai.UserConfigGroupDto), not marked as ignorable (13 known properties: "streamEnabled", "preferredModel", "groupName", "customBaseUrl", "updatedAt", "provider", "defaultTemperature", "testModel", "createdAt", "defaultTopP", "defaultMaxTokens", "timeoutSeconds", "systemPrompt"])
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 7, column: 17] (through reference chain: com.example.pure.model.dto.response.openai.UserConfigGroupDto["isActive"])
2025-08-01 18:04:49.865 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-08-01 18:04:49.865 [1;31mERROR[0;39m 15924 --- [http-nio-8080-exec-4] c.e.p.e.GlobalExceptionHandler : 服务器处理API出现异常
org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unrecognized field "isActive" (class com.example.pure.model.dto.response.openai.UserConfigGroupDto), not marked as ignorable; nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "isActive" (class com.example.pure.model.dto.response.openai.UserConfigGroupDto), not marked as ignorable (13 known properties: "streamEnabled", "preferredModel", "groupName", "customBaseUrl", "updatedAt", "provider", "defaultTemperature", "testModel", "createdAt", "defaultTopP", "defaultMaxTokens", "timeoutSeconds", "systemPrompt"])
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 7, column: 17] (through reference chain: com.example.pure.model.dto.response.openai.UserConfigGroupDto["isActive"])
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:391)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.read(AbstractJackson2HttpMessageConverter.java:343)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodArgumentResolver.readWithMessageConverters(AbstractMessageConverterMethodArgumentResolver.java:185)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:160)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:133)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:179)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:146)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:558)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "isActive" (class com.example.pure.model.dto.response.openai.UserConfigGroupDto), not marked as ignorable (13 known properties: "streamEnabled", "preferredModel", "groupName", "customBaseUrl", "updatedAt", "provider", "defaultTemperature", "testModel", "createdAt", "defaultTopP", "defaultMaxTokens", "timeoutSeconds", "systemPrompt"])
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 7, column: 17] (through reference chain: com.example.pure.model.dto.response.openai.UserConfigGroupDto["isActive"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:1127)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:2036)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1700)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1678)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:320)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3682)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:380)
	... 105 common frames omitted
2025-08-01 18:04:49.867 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-08-01 18:04:49.867 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [Result(code=500, message=服务器处理API出现异常，请联系管理员, success=false, data=null, time=2025-08-01T10:04:49.866 (truncated)...]
2025-08-01 18:04:49.868 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unrecognized field "isActive" (class com.example.pure.model.dto.response.openai.UserConfigGroupDto), not marked as ignorable; nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "isActive" (class com.example.pure.model.dto.response.openai.UserConfigGroupDto), not marked as ignorable (13 known properties: "streamEnabled", "preferredModel", "groupName", "customBaseUrl", "updatedAt", "provider", "defaultTemperature", "testModel", "createdAt", "defaultTopP", "defaultMaxTokens", "timeoutSeconds", "systemPrompt"])<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 7, column: 17] (through reference chain: com.example.pure.model.dto.response.openai.UserConfigGroupDto["isActive"])]
2025-08-01 18:04:49.868 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 500 INTERNAL_SERVER_ERROR
2025-08-01 18:04:49.868 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-01 18:05:25.123 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing PUT /api/ai/config/config-groups
2025-08-01 18:05:25.123 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-01 18:05:25.124 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#createOrUpdateUserConfigGroup(UserConfigGroupDto, Authentication)
2025-08-01 18:05:25.201 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-08-01 18:05:25.203 [34mINFO [0;39m 15924 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-08-01 18:05:25.203 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:05:25.204 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4669ff45]
2025-08-01 18:05:25.204 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1779284643 wrapping com.mysql.cj.jdbc.ConnectionImpl@6c6dc90e] will be managed by Spring
2025-08-01 18:05:25.204 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-08-01 18:05:25.204 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-08-01 18:05:25.206 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-08-01 18:05:25.206 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4669ff45]
2025-08-01 18:05:25.206 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-08-01 18:05:25.206 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4669ff45]
2025-08-01 18:05:25.206 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4669ff45]
2025-08-01 18:05:25.206 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4669ff45]
2025-08-01 18:05:25.208 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-08-01 18:05:25.208 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [PUT /api/ai/config/config-groups] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-01 18:05:25.208 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured PUT /api/ai/config/config-groups
2025-08-01 18:05:25.209 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : PUT "/api/ai/config/config-groups", parameters={}
2025-08-01 18:05:25.209 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#createOrUpdateUserConfigGroup(UserConfigGroupDto, Authentication)
2025-08-01 18:05:25.216 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [UserConfigGroupDto(groupName=key1, provider=GOOGLE, customBaseUrl=https://generativelanguage.googlea (truncated)...]
2025-08-01 18:05:25.289 [34mINFO [0;39m 15924 --- [http-nio-8080-exec-6] c.e.p.s.o.impl.AiConfigServiceImpl : 创建或更新用户AI配置分组 - 用户ID: 1, 分组: key1
2025-08-01 18:05:25.289 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:05:25.289 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b42c9bc]
2025-08-01 18:05:25.290 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@6c6dc90e] will be managed by Spring
2025-08-01 18:05:25.290 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] c.e.p.m.p.U.existsByUserIdAndGroupName : ==>  Preparing: SELECT COUNT(1) > 0 FROM user_ai_config_groups WHERE user_id = ? AND group_name = ?
2025-08-01 18:05:25.290 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] c.e.p.m.p.U.existsByUserIdAndGroupName : ==> Parameters: 1(Long), key1(String)
2025-08-01 18:05:25.316 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b42c9bc]
2025-08-01 18:05:25.348 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b42c9bc]
2025-08-01 18:05:25.348 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b42c9bc]
2025-08-01 18:05:25.351 [1;31mERROR[0;39m 15924 --- [http-nio-8080-exec-6] c.e.p.c.openai.AiConfigController : 创建或更新用户AI配置分组失败
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'group_name' in 'where clause'
### The error may exist in file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(1) > 0         FROM user_ai_config_groups         WHERE user_id = ? AND group_name = ?
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'group_name' in 'where clause'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'group_name' in 'where clause'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:236)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy109.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:142)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy2/jdk.proxy2.$Proxy146.existsByUserIdAndGroupName(Unknown Source)
	at com.example.pure.service.openai.impl.AiConfigServiceImpl.createOrUpdateUserConfigGroup(AiConfigServiceImpl.java:67)
	at com.example.pure.service.openai.impl.AiConfigServiceImpl$$FastClassBySpringCGLIB$$96c8d03b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.openai.impl.AiConfigServiceImpl$$EnhancerBySpringCGLIB$$bbfcdaeb.createOrUpdateUserConfigGroup(<generated>)
	at com.example.pure.controller.openai.AiConfigController.createOrUpdateUserConfigGroup(AiConfigController.java:111)
	at com.example.pure.controller.openai.AiConfigController$$FastClassBySpringCGLIB$$c20bcb15.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.openai.AiConfigController$$EnhancerBySpringCGLIB$$c07642e1.createOrUpdateUserConfigGroup(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:558)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'group_name' in 'where clause'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:114)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:990)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:384)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy3/jdk.proxy3.$Proxy203.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:75)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 134 common frames omitted
2025-08-01 18:05:25.353 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-08-01 18:05:25.354 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=500, message=操作失败: <EOL><EOL>### Error querying database.  Cause: java.sql.SQLSyntaxErrorExcepti (truncated)...]
2025-08-01 18:05:25.355 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-08-01 18:05:25.355 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-01 18:08:06.016 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing PUT /api/ai/config/config-groups
2025-08-01 18:08:06.016 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-01 18:08:06.016 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#createOrUpdateUserConfigGroup(UserConfigGroupDto, Authentication)
2025-08-01 18:08:06.094 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-08-01 18:08:06.097 [34mINFO [0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-08-01 18:08:06.097 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:08:06.097 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19374e5c]
2025-08-01 18:08:06.097 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2133642002 wrapping com.mysql.cj.jdbc.ConnectionImpl@6c6dc90e] will be managed by Spring
2025-08-01 18:08:06.097 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-08-01 18:08:06.098 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-08-01 18:08:06.100 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-08-01 18:08:06.100 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19374e5c]
2025-08-01 18:08:06.100 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-08-01 18:08:06.100 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19374e5c]
2025-08-01 18:08:06.100 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19374e5c]
2025-08-01 18:08:06.100 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19374e5c]
2025-08-01 18:08:06.103 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-08-01 18:08:06.103 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [PUT /api/ai/config/config-groups] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-01 18:08:06.103 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured PUT /api/ai/config/config-groups
2025-08-01 18:08:06.103 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : PUT "/api/ai/config/config-groups", parameters={}
2025-08-01 18:08:06.103 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#createOrUpdateUserConfigGroup(UserConfigGroupDto, Authentication)
2025-08-01 18:08:06.104 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [UserConfigGroupDto(groupName=key1, provider=GOOGLE, customBaseUrl=https://generativelanguage.googlea (truncated)...]
2025-08-01 18:08:06.106 [34mINFO [0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.s.o.impl.AiConfigServiceImpl : 创建或更新用户AI配置分组 - 用户ID: 1, 分组: key1
2025-08-01 18:08:06.106 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:08:06.106 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2fcf562a]
2025-08-01 18:08:06.106 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@366178279 wrapping com.mysql.cj.jdbc.ConnectionImpl@6c6dc90e] will be managed by Spring
2025-08-01 18:08:06.106 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.m.p.U.existsByUserIdAndGroupName : ==>  Preparing: SELECT COUNT(1) > 0 FROM user_ai_config_groups WHERE user_id = ? AND group_name = ?
2025-08-01 18:08:06.107 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.m.p.U.existsByUserIdAndGroupName : ==> Parameters: 1(Long), key1(String)
2025-08-01 18:08:06.109 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.m.p.U.existsByUserIdAndGroupName : <==      Total: 1
2025-08-01 18:08:06.109 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2fcf562a]
2025-08-01 18:08:06.110 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2fcf562a] from current transaction
2025-08-01 18:08:06.110 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.m.p.UserAiConfigMapper.insert : ==>  Preparing: INSERT INTO user_ai_config_groups ( user_id, group_name, provider, custom_base_url, test_model, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-08-01 18:08:06.111 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.m.p.UserAiConfigMapper.insert : ==> Parameters: 1(Long), key1(String), GOOGLE(String), https://generativelanguage.googleapis.com/v1beta/openai(String), gemini-2.5-flash(String), gemini-2.5-pro(String), null, null, null, true(Boolean), 60(Integer), you are a helpful assistant(String)
2025-08-01 18:08:06.115 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.m.p.UserAiConfigMapper.insert : <==    Updates: 1
2025-08-01 18:08:06.118 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2fcf562a]
2025-08-01 18:08:06.118 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.s.o.impl.AiConfigServiceImpl : 获取用户AI配置分组 - 用户ID: 1, 分组: key1
2025-08-01 18:08:06.118 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2fcf562a] from current transaction
2025-08-01 18:08:06.118 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.m.p.U.selectByUserIdAndGroupName : ==>  Preparing: SELECT id, user_id, group_name, provider, custom_base_url, test_model, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt, created_at, updated_at FROM user_ai_config_groups WHERE user_id = ? AND group_name = ?
2025-08-01 18:08:06.118 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.m.p.U.selectByUserIdAndGroupName : ==> Parameters: 1(Long), key1(String)
2025-08-01 18:08:06.123 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.m.p.U.selectByUserIdAndGroupName : <==      Total: 1
2025-08-01 18:08:06.123 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2fcf562a]
2025-08-01 18:08:06.123 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2fcf562a]
2025-08-01 18:08:06.123 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2fcf562a]
2025-08-01 18:08:06.123 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2fcf562a]
2025-08-01 18:08:06.129 [34mINFO [0;39m 15924 --- [http-nio-8080-exec-7] c.e.p.c.openai.AiConfigController : 创建或更新用户AI配置分组成功 - 用户ID: 1, 分组: key1
2025-08-01 18:08:06.130 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-08-01 18:08:06.130 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UserConfigGroupDto(groupName=key1, provider=GOOGLE (truncated)...]
2025-08-01 18:08:06.133 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-08-01 18:08:06.133 [39mDEBUG[0;39m 15924 --- [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-01 18:12:18.485 [39mDEBUG[0;39m 15924 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-01 18:12:18.485 [39mDEBUG[0;39m 15924 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-01 18:12:18.485 [34mINFO [0;39m 15924 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-08-01 18:12:18.485 [34mINFO [0;39m 15924 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e7cdb2]]
2025-08-01 18:12:18.485 [39mDEBUG[0;39m 15924 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e7cdb2]
2025-08-01 18:12:18.485 [39mDEBUG[0;39m 15924 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e7cdb2]
2025-08-01 18:12:18.485 [34mINFO [0;39m 15924 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-08-01 18:12:18.485 [39mDEBUG[0;39m 15924 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-01 18:12:18.485 [39mDEBUG[0;39m 15924 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-01 18:12:18.635 [34mINFO [0;39m 15924 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-08-01 18:12:18.640 [34mINFO [0;39m 15924 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-08-01 18:31:16.849 [34mINFO [0;39m 13016 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-08-01 18:31:16.855 [34mINFO [0;39m 13016 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 13016 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-08-01 18:31:16.856 [39mDEBUG[0;39m 13016 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-01 18:31:16.856 [34mINFO [0;39m 13016 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-08-01 18:31:17.879 [34mINFO [0;39m 13016 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 18:31:17.882 [34mINFO [0;39m 13016 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 18:31:17.930 [34mINFO [0;39m 13016 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-08-01 18:31:18.063 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-08-01 18:31:18.063 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ApiKeyLoadBalanceMapper.class]
2025-08-01 18:31:18.063 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ChatSessionMapper.class]
2025-08-01 18:31:18.063 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\CompatibleApiKeyMapper.class]
2025-08-01 18:31:18.063 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-08-01 18:31:18.063 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-08-01 18:31:18.064 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-08-01 18:31:18.064 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-08-01 18:31:18.064 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-08-01 18:31:18.064 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-08-01 18:31:18.064 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-08-01 18:31:18.064 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-08-01 18:31:18.064 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-08-01 18:31:18.064 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserAiConfigMapper.class]
2025-08-01 18:31:18.064 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserApiKeyMapper.class]
2025-08-01 18:31:18.064 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-08-01 18:31:18.064 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-08-01 18:31:18.064 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-08-01 18:31:18.064 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-08-01 18:31:18.065 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-08-01 18:31:18.066 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-08-01 18:31:18.067 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'apiKeyLoadBalanceMapper' and 'com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper' mapperInterface
2025-08-01 18:31:18.067 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'apiKeyLoadBalanceMapper'.
2025-08-01 18:31:18.067 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'chatSessionMapper' and 'com.example.pure.mapper.primary.ChatSessionMapper' mapperInterface
2025-08-01 18:31:18.068 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'chatSessionMapper'.
2025-08-01 18:31:18.068 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'compatibleApiKeyMapper' and 'com.example.pure.mapper.primary.CompatibleApiKeyMapper' mapperInterface
2025-08-01 18:31:18.068 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'compatibleApiKeyMapper'.
2025-08-01 18:31:18.068 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-08-01 18:31:18.068 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-08-01 18:31:18.068 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-08-01 18:31:18.069 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-08-01 18:31:18.069 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-08-01 18:31:18.069 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-08-01 18:31:18.069 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-08-01 18:31:18.069 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-08-01 18:31:18.069 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-08-01 18:31:18.070 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-08-01 18:31:18.070 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-08-01 18:31:18.070 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-08-01 18:31:18.070 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-08-01 18:31:18.070 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-08-01 18:31:18.070 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-08-01 18:31:18.071 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-08-01 18:31:18.071 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-08-01 18:31:18.071 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-08-01 18:31:18.071 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userAiConfigMapper' and 'com.example.pure.mapper.primary.UserAiConfigMapper' mapperInterface
2025-08-01 18:31:18.072 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userAiConfigMapper'.
2025-08-01 18:31:18.072 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userApiKeyMapper' and 'com.example.pure.mapper.primary.UserApiKeyMapper' mapperInterface
2025-08-01 18:31:18.072 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userApiKeyMapper'.
2025-08-01 18:31:18.072 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-08-01 18:31:18.073 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-08-01 18:31:18.073 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-08-01 18:31:18.073 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-08-01 18:31:18.073 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-08-01 18:31:18.073 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-08-01 18:31:18.073 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-08-01 18:31:18.074 [39mDEBUG[0;39m 13016 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-08-01 18:31:18.705 [34mINFO [0;39m 13016 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-08-01 18:31:18.712 [34mINFO [0;39m 13016 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 18:31:18.713 [34mINFO [0;39m 13016 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-08-01 18:31:18.714 [34mINFO [0;39m 13016 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-01 18:31:18.826 [34mINFO [0;39m 13016 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-08-01 18:31:18.827 [34mINFO [0;39m 13016 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1928 ms
2025-08-01 18:31:19.135 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-08-01 18:31:19.146 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ApiKeyLoadBalanceMapper.xml]'
2025-08-01 18:31:19.159 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ChatSessionMapper.xml]'
2025-08-01 18:31:19.169 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\CompatibleApiKeyMapper.xml]'
2025-08-01 18:31:19.179 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\MessagesMapper.xml]'
2025-08-01 18:31:19.185 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-08-01 18:31:19.191 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-08-01 18:31:19.205 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-08-01 18:31:19.209 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-08-01 18:31:19.215 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-08-01 18:31:19.220 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-08-01 18:31:19.226 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-08-01 18:31:19.241 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\RoleMapper.xml]'
2025-08-01 18:31:19.249 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]'
2025-08-01 18:31:19.256 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]'
2025-08-01 18:31:19.265 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserMapper.xml]'
2025-08-01 18:31:19.269 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-08-01 18:31:19.274 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-08-01 18:31:19.279 [39mDEBUG[0;39m 13016 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-08-01 18:31:19.294 [34mINFO [0;39m 13016 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-08-01 18:31:19.604 [34mINFO [0;39m 13016 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-08-01 18:31:20.274 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-08-01 18:31:20.275 [39mDEBUG[0;39m 13016 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-08-01 18:31:20.556 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-01 18:31:20.557 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-01 18:31:21.007 [34mINFO [0;39m 13016 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-08-01 18:31:21.149 [34mINFO [0;39m 13016 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-08-01 18:31:21.157 [34mINFO [0;39m 13016 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-08-01 18:31:21.209 [34mINFO [0;39m 13016 --- [main] c.e.pure.config.SnowflakeConfig : 初始化雪花算法ID生成器 - 机器ID: 1, 数据中心ID: 1
2025-08-01 18:31:21.209 [34mINFO [0;39m 13016 --- [main] c.e.pure.util.SnowflakeIdGenerator : 雪花算法ID生成器初始化 - 机器ID: 1, 数据中心ID: 1
2025-08-01 18:31:21.222 [34mINFO [0;39m 13016 --- [main] c.e.pure.util.SpringEncryptionUtil : Spring加密工具初始化成功（CBC模式）
2025-08-01 18:31:21.248 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-08-01 18:31:21.249 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-08-01 18:31:21.250 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-08-01 18:31:21.250 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-01 18:31:21.250 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-01 18:31:21.250 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-01 18:31:21.250 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-08-01 18:31:21.250 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-01 18:31:21.251 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-08-01 18:31:21.251 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-01 18:31:21.251 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-08-01 18:31:21.251 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-01 18:31:21.255 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-08-01 18:31:21.255 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-01 18:31:21.256 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-08-01 18:31:21.256 [39mDEBUG[0;39m 13016 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-08-01 18:31:21.362 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-08-01 18:31:21.362 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-08-01 18:31:21.365 [34mINFO [0;39m 13016 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@7a59780b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5205b3e2, org.springframework.security.web.context.SecurityContextPersistenceFilter@6ade4ac6, org.springframework.security.web.header.HeaderWriterFilter@581cb879, org.springframework.security.web.authentication.logout.LogoutFilter@1f50fe84, com.example.pure.filter.JwtFilter@226e95e9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3e3c6323, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6f404bc6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2802caa2, org.springframework.security.web.session.SessionManagementFilter@7843b65e, org.springframework.security.web.access.ExceptionTranslationFilter@3638120f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@118d7e0e]
2025-08-01 18:31:21.367 [34mINFO [0;39m 13016 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-08-01 18:31:21.369 [34mINFO [0;39m 13016 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-08-01 18:31:21.370 [34mINFO [0;39m 13016 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-08-01 18:31:21.370 [34mINFO [0;39m 13016 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-08-01 18:31:21.565 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-08-01 18:31:21.582 [34mINFO [0;39m 13016 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-08-01 18:31:21.644 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 105 mappings in 'requestMappingHandlerMapping'
2025-08-01 18:31:21.651 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-08-01 18:31:22.081 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-08-01 18:31:22.239 [34mINFO [0;39m 13016 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-08-01 18:31:22.259 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-08-01 18:31:22.259 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai']
2025-08-01 18:31:22.260 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v1/**']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/ai/config/**']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-08-01 18:31:22.261 [39mDEBUG[0;39m 13016 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-08-01 18:31:22.262 [34mINFO [0;39m 13016 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3d287f7c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@45aa02de, org.springframework.security.web.context.SecurityContextPersistenceFilter@709353b9, org.springframework.security.web.header.HeaderWriterFilter@203ba07, org.springframework.web.filter.CorsFilter@19598b38, org.springframework.security.web.authentication.logout.LogoutFilter@7b8b755d, com.example.pure.filter.JwtFilter@226e95e9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@69c53b72, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@768967cf, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5d16bb07, org.springframework.security.web.session.SessionManagementFilter@5e572ad6, org.springframework.security.web.access.ExceptionTranslationFilter@1c00a9f0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@36ce48bc]
2025-08-01 18:31:22.299 [39mTRACE[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@e6516e, started on Fri Aug 01 18:31:16 CST 2025
2025-08-01 18:31:22.314 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.AuthController:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OAuth2Controller:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OperatingLogController:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRCodeController:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRLoginController:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.VerificationController:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.s.R2Controller:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.DownloadController:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.FileManagerController:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.ImageController:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureFileManagerController:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureImageFileController:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.VideoController:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.m.MessagesController:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiChatController:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiConfigController:
	
2025-08-01 18:31:22.315 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiSystemTestController:
	
2025-08-01 18:31:22.316 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiCompatibleController:
	
2025-08-01 18:31:22.316 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserController:
	
2025-08-01 18:31:22.320 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
2025-08-01 18:31:22.320 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserProfileController:
	
2025-08-01 18:31:22.320 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoInteractionController:
	
2025-08-01 18:31:22.321 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoUrlController:
	
2025-08-01 18:31:22.321 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.VideoCommentInteractionController:
	
2025-08-01 18:31:22.322 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-08-01 18:31:22.323 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-08-01 18:31:22.323 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-08-01 18:31:22.323 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-08-01 18:31:22.418 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-01 18:31:22.454 [39mDEBUG[0;39m 13016 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-01 18:31:22.729 [34mINFO [0;39m 13016 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 18:31:22.739 [34mINFO [0;39m 13016 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 18:31:22.740 [39mDEBUG[0;39m 13016 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-01 18:31:22.740 [39mDEBUG[0;39m 13016 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-01 18:31:22.740 [34mINFO [0;39m 13016 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-08-01 18:31:22.740 [39mDEBUG[0;39m 13016 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6e0fd2dc]
2025-08-01 18:31:22.740 [39mDEBUG[0;39m 13016 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6e0fd2dc]
2025-08-01 18:31:22.740 [34mINFO [0;39m 13016 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6e0fd2dc]]
2025-08-01 18:31:22.741 [34mINFO [0;39m 13016 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-08-01 18:31:22.741 [39mDEBUG[0;39m 13016 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-01 18:31:22.741 [39mDEBUG[0;39m 13016 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-01 18:31:22.753 [34mINFO [0;39m 13016 --- [main] com.example.pure.PureApplication : Started PureApplication in 6.324 seconds (JVM running for 6.902)
2025-08-01 18:32:22.289 [34mINFO [0;39m 13016 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-08-01 18:32:24.493 [39mDEBUG[0;39m 13016 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-01 18:32:24.493 [39mDEBUG[0;39m 13016 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-01 18:32:24.493 [34mINFO [0;39m 13016 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-08-01 18:32:24.493 [34mINFO [0;39m 13016 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6e0fd2dc]]
2025-08-01 18:32:24.493 [39mDEBUG[0;39m 13016 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6e0fd2dc]
2025-08-01 18:32:24.493 [39mDEBUG[0;39m 13016 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6e0fd2dc]
2025-08-01 18:32:24.493 [34mINFO [0;39m 13016 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-08-01 18:32:24.494 [39mDEBUG[0;39m 13016 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-01 18:32:24.494 [39mDEBUG[0;39m 13016 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-01 18:32:24.539 [34mINFO [0;39m 13016 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-08-01 18:32:24.545 [34mINFO [0;39m 13016 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-08-01 18:34:22.921 [34mINFO [0;39m 11336 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-08-01 18:34:22.926 [34mINFO [0;39m 11336 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 11336 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-08-01 18:34:22.927 [39mDEBUG[0;39m 11336 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-01 18:34:22.927 [34mINFO [0;39m 11336 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-08-01 18:34:23.890 [34mINFO [0;39m 11336 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 18:34:23.892 [34mINFO [0;39m 11336 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 18:34:23.937 [34mINFO [0;39m 11336 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-08-01 18:34:24.029 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-08-01 18:34:24.029 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ApiKeyLoadBalanceMapper.class]
2025-08-01 18:34:24.029 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ChatSessionMapper.class]
2025-08-01 18:34:24.029 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\CompatibleApiKeyMapper.class]
2025-08-01 18:34:24.029 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-08-01 18:34:24.029 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-08-01 18:34:24.030 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-08-01 18:34:24.030 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-08-01 18:34:24.030 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-08-01 18:34:24.030 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-08-01 18:34:24.030 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-08-01 18:34:24.030 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-08-01 18:34:24.030 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-08-01 18:34:24.030 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserAiConfigMapper.class]
2025-08-01 18:34:24.030 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserApiKeyMapper.class]
2025-08-01 18:34:24.030 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-08-01 18:34:24.030 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-08-01 18:34:24.030 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-08-01 18:34:24.030 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-08-01 18:34:24.031 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-08-01 18:34:24.032 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-08-01 18:34:24.032 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'apiKeyLoadBalanceMapper' and 'com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper' mapperInterface
2025-08-01 18:34:24.032 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'apiKeyLoadBalanceMapper'.
2025-08-01 18:34:24.032 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'chatSessionMapper' and 'com.example.pure.mapper.primary.ChatSessionMapper' mapperInterface
2025-08-01 18:34:24.033 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'chatSessionMapper'.
2025-08-01 18:34:24.033 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'compatibleApiKeyMapper' and 'com.example.pure.mapper.primary.CompatibleApiKeyMapper' mapperInterface
2025-08-01 18:34:24.033 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'compatibleApiKeyMapper'.
2025-08-01 18:34:24.033 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-08-01 18:34:24.033 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-08-01 18:34:24.033 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-08-01 18:34:24.033 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-08-01 18:34:24.034 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-08-01 18:34:24.034 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-08-01 18:34:24.034 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-08-01 18:34:24.034 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-08-01 18:34:24.034 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-08-01 18:34:24.034 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-08-01 18:34:24.034 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-08-01 18:34:24.035 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-08-01 18:34:24.035 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-08-01 18:34:24.035 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-08-01 18:34:24.035 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-08-01 18:34:24.035 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-08-01 18:34:24.035 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-08-01 18:34:24.035 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-08-01 18:34:24.035 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userAiConfigMapper' and 'com.example.pure.mapper.primary.UserAiConfigMapper' mapperInterface
2025-08-01 18:34:24.036 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userAiConfigMapper'.
2025-08-01 18:34:24.036 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userApiKeyMapper' and 'com.example.pure.mapper.primary.UserApiKeyMapper' mapperInterface
2025-08-01 18:34:24.036 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userApiKeyMapper'.
2025-08-01 18:34:24.036 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-08-01 18:34:24.036 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-08-01 18:34:24.036 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-08-01 18:34:24.037 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-08-01 18:34:24.037 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-08-01 18:34:24.037 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-08-01 18:34:24.037 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-08-01 18:34:24.038 [39mDEBUG[0;39m 11336 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-08-01 18:34:24.667 [34mINFO [0;39m 11336 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-08-01 18:34:24.673 [34mINFO [0;39m 11336 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 18:34:24.674 [34mINFO [0;39m 11336 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-08-01 18:34:24.674 [34mINFO [0;39m 11336 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-01 18:34:24.770 [34mINFO [0;39m 11336 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-08-01 18:34:24.770 [34mINFO [0;39m 11336 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1800 ms
2025-08-01 18:34:25.036 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-08-01 18:34:25.046 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ApiKeyLoadBalanceMapper.xml]'
2025-08-01 18:34:25.056 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ChatSessionMapper.xml]'
2025-08-01 18:34:25.064 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\CompatibleApiKeyMapper.xml]'
2025-08-01 18:34:25.071 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\MessagesMapper.xml]'
2025-08-01 18:34:25.075 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-08-01 18:34:25.079 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-08-01 18:34:25.088 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-08-01 18:34:25.100 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-08-01 18:34:25.105 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-08-01 18:34:25.109 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-08-01 18:34:25.115 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-08-01 18:34:25.121 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\RoleMapper.xml]'
2025-08-01 18:34:25.128 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]'
2025-08-01 18:34:25.133 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]'
2025-08-01 18:34:25.142 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserMapper.xml]'
2025-08-01 18:34:25.153 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-08-01 18:34:25.157 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-08-01 18:34:25.162 [39mDEBUG[0;39m 11336 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-08-01 18:34:25.176 [34mINFO [0;39m 11336 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-08-01 18:34:25.469 [34mINFO [0;39m 11336 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-08-01 18:34:26.395 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-08-01 18:34:26.395 [39mDEBUG[0;39m 11336 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-08-01 18:34:26.739 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-01 18:34:26.741 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-01 18:34:27.243 [34mINFO [0;39m 11336 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-08-01 18:34:27.406 [34mINFO [0;39m 11336 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-08-01 18:34:27.415 [34mINFO [0;39m 11336 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-08-01 18:34:27.493 [34mINFO [0;39m 11336 --- [main] c.e.pure.config.SnowflakeConfig : 初始化雪花算法ID生成器 - 机器ID: 1, 数据中心ID: 1
2025-08-01 18:34:27.494 [34mINFO [0;39m 11336 --- [main] c.e.pure.util.SnowflakeIdGenerator : 雪花算法ID生成器初始化 - 机器ID: 1, 数据中心ID: 1
2025-08-01 18:34:27.506 [34mINFO [0;39m 11336 --- [main] c.e.pure.util.SpringEncryptionUtil : Spring加密工具初始化成功（CBC模式）
2025-08-01 18:34:27.534 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-08-01 18:34:27.534 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-01 18:34:27.535 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-01 18:34:27.535 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-01 18:34:27.535 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-08-01 18:34:27.536 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-08-01 18:34:27.536 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-08-01 18:34:27.536 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-01 18:34:27.536 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-08-01 18:34:27.536 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-01 18:34:27.537 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-08-01 18:34:27.537 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-01 18:34:27.542 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-08-01 18:34:27.542 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-08-01 18:34:27.543 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-08-01 18:34:27.543 [39mDEBUG[0;39m 11336 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-01 18:34:27.695 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-08-01 18:34:27.696 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-08-01 18:34:27.700 [34mINFO [0;39m 11336 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@39adf4e6, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@73299197, org.springframework.security.web.context.SecurityContextPersistenceFilter@4700064f, org.springframework.security.web.header.HeaderWriterFilter@3e9e7a70, org.springframework.security.web.authentication.logout.LogoutFilter@541c76fd, com.example.pure.filter.JwtFilter@4bc41565, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6fc84468, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2f6ede9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4ef7145f, org.springframework.security.web.session.SessionManagementFilter@7a458c73, org.springframework.security.web.access.ExceptionTranslationFilter@124b8fe9, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@37e15055]
2025-08-01 18:34:27.704 [34mINFO [0;39m 11336 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-08-01 18:34:27.707 [34mINFO [0;39m 11336 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-08-01 18:34:27.709 [34mINFO [0;39m 11336 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-08-01 18:34:27.711 [34mINFO [0;39m 11336 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-08-01 18:34:28.164 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-08-01 18:34:28.196 [34mINFO [0;39m 11336 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-08-01 18:34:28.326 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 105 mappings in 'requestMappingHandlerMapping'
2025-08-01 18:34:28.338 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-08-01 18:34:28.823 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-08-01 18:34:28.941 [34mINFO [0;39m 11336 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-08-01 18:34:28.959 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v1/**']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/ai/config/**']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-08-01 18:34:28.960 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-08-01 18:34:28.961 [39mDEBUG[0;39m 11336 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-08-01 18:34:28.961 [34mINFO [0;39m 11336 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@50cb64d5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@9be2897, org.springframework.security.web.context.SecurityContextPersistenceFilter@7ea91c39, org.springframework.security.web.header.HeaderWriterFilter@54626326, org.springframework.web.filter.CorsFilter@3e1c566d, org.springframework.security.web.authentication.logout.LogoutFilter@3a8cc099, com.example.pure.filter.JwtFilter@4bc41565, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@55d35f7a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@12ef7db5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@36ce48bc, org.springframework.security.web.session.SessionManagementFilter@6f4e39b3, org.springframework.security.web.access.ExceptionTranslationFilter@6179551b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2e04dbc1]
2025-08-01 18:34:28.993 [39mTRACE[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@43ed0ff3, started on Fri Aug 01 18:34:22 CST 2025
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.AuthController:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OAuth2Controller:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OperatingLogController:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRCodeController:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRLoginController:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.VerificationController:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.s.R2Controller:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.DownloadController:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.FileManagerController:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.ImageController:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureFileManagerController:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureImageFileController:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.VideoController:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.m.MessagesController:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiChatController:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiConfigController:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiSystemTestController:
	
2025-08-01 18:34:29.008 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiCompatibleController:
	
2025-08-01 18:34:29.009 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserController:
	
2025-08-01 18:34:29.011 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.WebSocketTestController:
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
2025-08-01 18:34:29.012 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserProfileController:
	
2025-08-01 18:34:29.012 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoInteractionController:
	
2025-08-01 18:34:29.012 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoUrlController:
	
2025-08-01 18:34:29.012 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.VideoCommentInteractionController:
	
2025-08-01 18:34:29.013 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-08-01 18:34:29.013 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-08-01 18:34:29.013 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-08-01 18:34:29.013 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-08-01 18:34:29.109 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-01 18:34:29.159 [39mDEBUG[0;39m 11336 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-01 18:34:29.993 [34mINFO [0;39m 11336 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 18:34:30.008 [34mINFO [0;39m 11336 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 18:34:30.010 [39mDEBUG[0;39m 11336 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-01 18:34:30.010 [39mDEBUG[0;39m 11336 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-01 18:34:30.011 [34mINFO [0;39m 11336 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-08-01 18:34:30.011 [39mDEBUG[0;39m 11336 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@14d5b2ac]
2025-08-01 18:34:30.011 [39mDEBUG[0;39m 11336 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@14d5b2ac]
2025-08-01 18:34:30.011 [34mINFO [0;39m 11336 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@14d5b2ac]]
2025-08-01 18:34:30.011 [34mINFO [0;39m 11336 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-08-01 18:34:30.011 [39mDEBUG[0;39m 11336 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-01 18:34:30.011 [39mDEBUG[0;39m 11336 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-01 18:34:30.037 [34mINFO [0;39m 11336 --- [main] com.example.pure.PureApplication : Started PureApplication in 7.529 seconds (JVM running for 8.053)
2025-08-01 18:35:28.980 [34mINFO [0;39m 11336 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-08-01 18:36:06.710 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-9] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 18:36:06.710 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-08-01 18:36:06.710 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-08-01 18:36:06.710 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-08-01 18:36:06.710 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-08-01 18:36:06.712 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@5fec8fe4
2025-08-01 18:36:06.713 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@560c86f4
2025-08-01 18:36:06.713 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-08-01 18:36:06.713 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed initialization in 3 ms
2025-08-01 18:36:06.725 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing POST /api/ai/config/api-keys/batch
2025-08-01 18:36:06.729 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-01 18:36:06.737 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-01 18:36:06.740 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-08-01 18:36:06.746 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Failed to authorize filter invocation [POST /api/ai/config/api-keys/batch] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-01 18:36:06.747 [31mWARN [0;39m 11336 --- [http-nio-8080-exec-9] c.e.p.h.CustomAuthenticationEntryPoint : 认证失败: Full authentication is required to access this resource, URI: /api/ai/config/api-keys/batch
2025-08-01 18:36:06.785 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-01 18:36:36.453 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing POST /api/ai/config/api-keys/batch
2025-08-01 18:36:36.453 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-01 18:36:36.453 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-01 18:36:36.901 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-08-01 18:36:36.932 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-08-01 18:36:36.938 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:36:36.941 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@23816157]
2025-08-01 18:36:36.947 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@714323626 wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will be managed by Spring
2025-08-01 18:36:36.949 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-08-01 18:36:36.969 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-08-01 18:36:36.992 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-08-01 18:36:36.993 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@23816157]
2025-08-01 18:36:36.994 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-08-01 18:36:36.995 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@23816157]
2025-08-01 18:36:36.995 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@23816157]
2025-08-01 18:36:36.995 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@23816157]
2025-08-01 18:36:36.999 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-08-01 18:36:37.000 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai/config/api-keys/batch] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-01 18:36:37.000 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured POST /api/ai/config/api-keys/batch
2025-08-01 18:36:37.003 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : POST "/api/ai/config/api-keys/batch", parameters={}
2025-08-01 18:36:37.004 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-01 18:36:37.024 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [BatchAddApiKeyRequest(configGroupId=1, apiKeys=[AIzaSyBRV9C_xUM3MNSFlWuVhrhIK3JMufmPr7I, AIzaSyBADRs (truncated)...]
2025-08-01 18:36:37.112 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥 - 用户ID: 1, 配置分组ID: 1, 数量: 3
2025-08-01 18:36:37.113 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:36:37.113 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad]
2025-08-01 18:36:37.113 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@78883239 wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will be managed by Spring
2025-08-01 18:36:37.113 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.U.selectByUserId : ==>  Preparing: SELECT id, user_id, group_name, provider, custom_base_url, test_model, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt, created_at, updated_at FROM user_ai_config_groups WHERE user_id = ? ORDER BY created_at ASC
2025-08-01 18:36:37.114 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.U.selectByUserId : ==> Parameters: 1(Long)
2025-08-01 18:36:37.119 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.U.selectByUserId : <==      Total: 1
2025-08-01 18:36:37.119 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad]
2025-08-01 18:36:37.120 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : 验证API密钥 - 提供商: GOOGLE
2025-08-01 18:36:37.121 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google API密钥验证详情 ===
2025-08-01 18:36:37.121 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : 验证URL: https://generativelanguage.googleapis.com/v1beta/openai/models
2025-08-01 18:36:37.121 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyBRV9...
2025-08-01 18:36:37.159 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : GoogleAPI密钥验证开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/models
2025-08-01 18:36:37.185 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.s.w.r.f.client.ExchangeFunctions : [6f0379d3] HTTP GET https://generativelanguage.googleapis.com/v1beta/openai/models
2025-08-01 18:36:37.890 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [6f0379d3] [e2352b39-1] Response 200 OK
2025-08-01 18:36:37.909 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [6f0379d3] [e2352b39-1] Decoded "{<EOL>  "object": "list",<EOL>  "data": [<EOL>    {<EOL>      "id": "models/embedding-gecko-001",<EOL>      "object": "m (truncated)..."
2025-08-01 18:36:37.909 [34mINFO [0;39m 11336 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : GoogleAPI密钥验证成功 - 响应时间: 789ms
2025-08-01 18:36:37.909 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : GoogleAPI密钥验证响应: {
  "object": "list",
  "data": [
    {
      "id": "models/embedding-gecko-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-pro-latest",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-pro-002",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-pro",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash-latest",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash-002",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash-8b",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash-8b-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash-8b-latest",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-pro-preview-03-25",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-preview-05-20",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-lite-preview-06-17",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-pro-preview-05-06",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-pro-preview-06-05",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-pro",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-exp",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-exp-image-generation",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-lite-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-lite",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-preview-image-generation",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-lite-preview-02-05",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-lite-preview",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-pro-exp",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-pro-exp-02-05",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-exp-1206",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-thinking-exp-01-21",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-thinking-exp",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-thinking-exp-1219",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-preview-tts",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-pro-preview-tts",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/learnlm-2.0-flash-experimental",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3-1b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3-4b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3-12b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3-27b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3n-e4b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3n-e2b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-lite",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/embedding-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/text-embedding-004",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-embedding-exp-03-07",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-embedding-exp",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-embedding-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/aqa",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/imagen-3.0-generate-002",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/imagen-4.0-generate-preview-06-06",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/imagen-4.0-ultra-generate-preview-06-06",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/veo-2.0-generate-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/veo-3.0-generate-preview",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/veo-3.0-fast-generate-preview",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-preview-native-audio-dialog",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-exp-native-audio-thinking-dialog",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-live-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-live-2.5-flash-preview",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-live-preview",
      "object": "model",
      "owned_by": "google"
    }
  ]
}

2025-08-01 18:36:37.910 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad] from current transaction
2025-08-01 18:36:37.911 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.UserApiKeyMapper.insert : ==>  Preparing: INSERT INTO user_api_keys ( user_id, config_group_id, api_key_encrypted, is_active, priority ) VALUES ( ?, ?, ?, ?, ? )
2025-08-01 18:36:37.911 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.UserApiKeyMapper.insert : ==> Parameters: 1(Long), 1(Long), 5916c94648f1ca80f96f511a088c54c83605728b9cf9ea0820f9ec9552419a22d6dc535e4cd8c919dc83f5872312b19509b5aadf5f344335f743597de3322038(String), true(Boolean), 1(Integer)
2025-08-01 18:36:37.915 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.UserApiKeyMapper.insert : <==    Updates: 1
2025-08-01 18:36:37.917 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad]
2025-08-01 18:36:37.920 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态 - ID: 4
2025-08-01 18:36:37.920 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad] from current transaction
2025-08-01 18:36:37.920 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-01 18:36:37.920 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 4(Long)
2025-08-01 18:36:37.922 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 0
2025-08-01 18:36:37.923 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad]
2025-08-01 18:36:37.923 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad] from current transaction
2025-08-01 18:36:37.923 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.A.insert : ==>  Preparing: INSERT INTO api_key_load_balance ( user_id, provider, api_key_id, current_requests, total_requests, error_count, is_healthy ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-08-01 18:36:37.925 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad]
2025-08-01 18:36:37.927 [1;31mERROR[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态时发生错误 - ID: 4
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'provider' in 'class com.example.pure.model.entity.ApiKeyLoadBalance'
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy109.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:142)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy2/jdk.proxy2.$Proxy149.insert(Unknown Source)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl.initializeLoadBalanceState(LoadBalancerServiceImpl.java:317)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl$$FastClassBySpringCGLIB$$36b9f669.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl$$EnhancerBySpringCGLIB$$c1f6b0bd.initializeLoadBalanceState(<generated>)
	at com.example.pure.service.openai.impl.AiConfigServiceImpl.batchAddApiKeys(AiConfigServiceImpl.java:193)
	at com.example.pure.service.openai.impl.AiConfigServiceImpl$$FastClassBySpringCGLIB$$96c8d03b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.openai.impl.AiConfigServiceImpl$$EnhancerBySpringCGLIB$$4748f173.batchAddApiKeys(<generated>)
	at com.example.pure.controller.openai.AiConfigController.batchAddApiKeys(AiConfigController.java:210)
	at com.example.pure.controller.openai.AiConfigController$$FastClassBySpringCGLIB$$c20bcb15.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.openai.AiConfigController$$EnhancerBySpringCGLIB$$17c205c1.batchAddApiKeys(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'provider' in 'class com.example.pure.model.entity.ApiKeyLoadBalance'
	at org.apache.ibatis.reflection.Reflector.getGetInvoker(Reflector.java:387)
	at org.apache.ibatis.reflection.MetaClass.getGetInvoker(MetaClass.java:162)
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:159)
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49)
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:116)
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:79)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.parameterize(PreparedStatementHandler.java:97)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.parameterize(RoutingStatementHandler.java:65)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:91)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 147 common frames omitted
2025-08-01 18:36:37.928 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥添加成功 - 用户ID: 1, 配置分组ID: 1, 密钥: AIza****Pr7I
2025-08-01 18:36:37.928 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : 验证API密钥 - 提供商: GOOGLE
2025-08-01 18:36:37.928 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google API密钥验证详情 ===
2025-08-01 18:36:37.928 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : 验证URL: https://generativelanguage.googleapis.com/v1beta/openai/models
2025-08-01 18:36:37.929 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyBADR...
2025-08-01 18:36:37.929 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : GoogleAPI密钥验证开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/models
2025-08-01 18:36:37.929 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.s.w.r.f.client.ExchangeFunctions : [4f155996] HTTP GET https://generativelanguage.googleapis.com/v1beta/openai/models
2025-08-01 18:36:38.154 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [4f155996] [e2352b39-2] Response 200 OK
2025-08-01 18:36:38.156 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [4f155996] [e2352b39-2] Decoded "{<EOL>  "object": "list",<EOL>  "data": [<EOL>    {<EOL>      "id": "models/embedding-gecko-001",<EOL>      "object": "m (truncated)..."
2025-08-01 18:36:38.156 [34mINFO [0;39m 11336 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : GoogleAPI密钥验证成功 - 响应时间: 228ms
2025-08-01 18:36:38.156 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : GoogleAPI密钥验证响应: {
  "object": "list",
  "data": [
    {
      "id": "models/embedding-gecko-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-pro-latest",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-pro-002",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-pro",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash-latest",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash-002",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash-8b",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash-8b-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash-8b-latest",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-pro-preview-03-25",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-preview-05-20",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-lite-preview-06-17",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-pro-preview-05-06",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-pro-preview-06-05",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-pro",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-exp",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-exp-image-generation",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-lite-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-lite",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-preview-image-generation",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-lite-preview-02-05",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-lite-preview",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-pro-exp",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-pro-exp-02-05",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-exp-1206",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-thinking-exp-01-21",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-thinking-exp",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-thinking-exp-1219",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-preview-tts",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-pro-preview-tts",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/learnlm-2.0-flash-experimental",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3-1b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3-4b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3-12b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3-27b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3n-e4b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3n-e2b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-lite",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/embedding-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/text-embedding-004",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-embedding-exp-03-07",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-embedding-exp",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-embedding-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/aqa",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/imagen-3.0-generate-002",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/imagen-4.0-generate-preview-06-06",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/imagen-4.0-ultra-generate-preview-06-06",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/veo-2.0-generate-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/veo-3.0-generate-preview",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/veo-3.0-fast-generate-preview",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-preview-native-audio-dialog",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-exp-native-audio-thinking-dialog",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-live-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-live-2.5-flash-preview",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-live-preview",
      "object": "model",
      "owned_by": "google"
    }
  ]
}

2025-08-01 18:36:38.157 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad] from current transaction
2025-08-01 18:36:38.157 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.UserApiKeyMapper.insert : ==>  Preparing: INSERT INTO user_api_keys ( user_id, config_group_id, api_key_encrypted, is_active, priority ) VALUES ( ?, ?, ?, ?, ? )
2025-08-01 18:36:38.158 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.UserApiKeyMapper.insert : ==> Parameters: 1(Long), 1(Long), 335e29a48f97df2e41490fc794e1a33ba237ca766cfa5ac12b378a71f0ac8868e9e5b99ede1f5ea56bca7d853402e21f34827126e9de99396526cfa2de6c2470(String), true(Boolean), 1(Integer)
2025-08-01 18:36:38.162 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.UserApiKeyMapper.insert : <==    Updates: 1
2025-08-01 18:36:38.162 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad]
2025-08-01 18:36:38.162 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态 - ID: 5
2025-08-01 18:36:38.162 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad] from current transaction
2025-08-01 18:36:38.162 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-01 18:36:38.163 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 5(Long)
2025-08-01 18:36:38.165 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 0
2025-08-01 18:36:38.165 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad]
2025-08-01 18:36:38.165 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad] from current transaction
2025-08-01 18:36:38.165 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.A.insert : ==>  Preparing: INSERT INTO api_key_load_balance ( user_id, provider, api_key_id, current_requests, total_requests, error_count, is_healthy ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-08-01 18:36:38.166 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad]
2025-08-01 18:36:38.167 [1;31mERROR[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态时发生错误 - ID: 5
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'provider' in 'class com.example.pure.model.entity.ApiKeyLoadBalance'
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy109.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:142)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy2/jdk.proxy2.$Proxy149.insert(Unknown Source)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl.initializeLoadBalanceState(LoadBalancerServiceImpl.java:317)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl$$FastClassBySpringCGLIB$$36b9f669.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl$$EnhancerBySpringCGLIB$$c1f6b0bd.initializeLoadBalanceState(<generated>)
	at com.example.pure.service.openai.impl.AiConfigServiceImpl.batchAddApiKeys(AiConfigServiceImpl.java:193)
	at com.example.pure.service.openai.impl.AiConfigServiceImpl$$FastClassBySpringCGLIB$$96c8d03b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.openai.impl.AiConfigServiceImpl$$EnhancerBySpringCGLIB$$4748f173.batchAddApiKeys(<generated>)
	at com.example.pure.controller.openai.AiConfigController.batchAddApiKeys(AiConfigController.java:210)
	at com.example.pure.controller.openai.AiConfigController$$FastClassBySpringCGLIB$$c20bcb15.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.openai.AiConfigController$$EnhancerBySpringCGLIB$$17c205c1.batchAddApiKeys(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'provider' in 'class com.example.pure.model.entity.ApiKeyLoadBalance'
	at org.apache.ibatis.reflection.Reflector.getGetInvoker(Reflector.java:387)
	at org.apache.ibatis.reflection.MetaClass.getGetInvoker(MetaClass.java:162)
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:159)
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49)
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:116)
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:79)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.parameterize(PreparedStatementHandler.java:97)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.parameterize(RoutingStatementHandler.java:65)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:91)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 147 common frames omitted
2025-08-01 18:36:38.167 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥添加成功 - 用户ID: 1, 配置分组ID: 1, 密钥: AIza****ixyI
2025-08-01 18:36:38.167 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : 验证API密钥 - 提供商: GOOGLE
2025-08-01 18:36:38.167 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google API密钥验证详情 ===
2025-08-01 18:36:38.167 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : 验证URL: https://generativelanguage.googleapis.com/v1beta/openai/models
2025-08-01 18:36:38.167 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyDKiH...
2025-08-01 18:36:38.168 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : GoogleAPI密钥验证开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/models
2025-08-01 18:36:38.168 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.s.w.r.f.client.ExchangeFunctions : [12918e9e] HTTP GET https://generativelanguage.googleapis.com/v1beta/openai/models
2025-08-01 18:36:38.388 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [12918e9e] [e2352b39-3] Response 200 OK
2025-08-01 18:36:38.390 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [12918e9e] [e2352b39-3] Decoded "{<EOL>  "object": "list",<EOL>  "data": [<EOL>    {<EOL>      "id": "models/embedding-gecko-001",<EOL>      "object": "m (truncated)..."
2025-08-01 18:36:38.390 [34mINFO [0;39m 11336 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : GoogleAPI密钥验证成功 - 响应时间: 223ms
2025-08-01 18:36:38.391 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : GoogleAPI密钥验证响应: {
  "object": "list",
  "data": [
    {
      "id": "models/embedding-gecko-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-pro-latest",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-pro-002",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-pro",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash-latest",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash-002",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash-8b",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash-8b-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-1.5-flash-8b-latest",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-pro-preview-03-25",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-preview-05-20",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-lite-preview-06-17",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-pro-preview-05-06",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-pro-preview-06-05",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-pro",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-exp",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-exp-image-generation",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-lite-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-lite",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-preview-image-generation",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-lite-preview-02-05",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-lite-preview",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-pro-exp",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-pro-exp-02-05",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-exp-1206",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-thinking-exp-01-21",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-thinking-exp",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-thinking-exp-1219",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-preview-tts",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-pro-preview-tts",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/learnlm-2.0-flash-experimental",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3-1b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3-4b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3-12b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3-27b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3n-e4b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemma-3n-e2b-it",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-lite",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/embedding-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/text-embedding-004",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-embedding-exp-03-07",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-embedding-exp",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-embedding-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/aqa",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/imagen-3.0-generate-002",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/imagen-4.0-generate-preview-06-06",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/imagen-4.0-ultra-generate-preview-06-06",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/veo-2.0-generate-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/veo-3.0-generate-preview",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/veo-3.0-fast-generate-preview",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-preview-native-audio-dialog",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-exp-native-audio-thinking-dialog",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.0-flash-live-001",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-live-2.5-flash-preview",
      "object": "model",
      "owned_by": "google"
    },
    {
      "id": "models/gemini-2.5-flash-live-preview",
      "object": "model",
      "owned_by": "google"
    }
  ]
}

2025-08-01 18:36:38.391 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad] from current transaction
2025-08-01 18:36:38.391 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.UserApiKeyMapper.insert : ==>  Preparing: INSERT INTO user_api_keys ( user_id, config_group_id, api_key_encrypted, is_active, priority ) VALUES ( ?, ?, ?, ?, ? )
2025-08-01 18:36:38.391 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.UserApiKeyMapper.insert : ==> Parameters: 1(Long), 1(Long), 02a1b721d97c31593c6c0c68c8af5a777c559430d20c8095b842b66bc4e3c493c9aadbd800b8c2aa2fe8b5e3b5c87faa646abc093dcc0bd446fcc7420a9cdbd6(String), true(Boolean), 1(Integer)
2025-08-01 18:36:38.395 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.UserApiKeyMapper.insert : <==    Updates: 1
2025-08-01 18:36:38.395 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad]
2025-08-01 18:36:38.395 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态 - ID: 6
2025-08-01 18:36:38.395 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad] from current transaction
2025-08-01 18:36:38.395 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-01 18:36:38.395 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 6(Long)
2025-08-01 18:36:38.397 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 0
2025-08-01 18:36:38.397 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad]
2025-08-01 18:36:38.397 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad] from current transaction
2025-08-01 18:36:38.397 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.m.p.A.insert : ==>  Preparing: INSERT INTO api_key_load_balance ( user_id, provider, api_key_id, current_requests, total_requests, error_count, is_healthy ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-08-01 18:36:38.398 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad]
2025-08-01 18:36:38.398 [1;31mERROR[0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态时发生错误 - ID: 6
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'provider' in 'class com.example.pure.model.entity.ApiKeyLoadBalance'
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy109.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:142)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy2/jdk.proxy2.$Proxy149.insert(Unknown Source)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl.initializeLoadBalanceState(LoadBalancerServiceImpl.java:317)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl$$FastClassBySpringCGLIB$$36b9f669.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl$$EnhancerBySpringCGLIB$$c1f6b0bd.initializeLoadBalanceState(<generated>)
	at com.example.pure.service.openai.impl.AiConfigServiceImpl.batchAddApiKeys(AiConfigServiceImpl.java:193)
	at com.example.pure.service.openai.impl.AiConfigServiceImpl$$FastClassBySpringCGLIB$$96c8d03b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.openai.impl.AiConfigServiceImpl$$EnhancerBySpringCGLIB$$4748f173.batchAddApiKeys(<generated>)
	at com.example.pure.controller.openai.AiConfigController.batchAddApiKeys(AiConfigController.java:210)
	at com.example.pure.controller.openai.AiConfigController$$FastClassBySpringCGLIB$$c20bcb15.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.openai.AiConfigController$$EnhancerBySpringCGLIB$$17c205c1.batchAddApiKeys(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'provider' in 'class com.example.pure.model.entity.ApiKeyLoadBalance'
	at org.apache.ibatis.reflection.Reflector.getGetInvoker(Reflector.java:387)
	at org.apache.ibatis.reflection.MetaClass.getGetInvoker(MetaClass.java:162)
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:159)
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49)
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:116)
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:79)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.parameterize(PreparedStatementHandler.java:97)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.parameterize(RoutingStatementHandler.java:65)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:91)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 147 common frames omitted
2025-08-01 18:36:38.399 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥添加成功 - 用户ID: 1, 配置分组ID: 1, 密钥: AIza****wmUU
2025-08-01 18:36:38.399 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 1, 总数: 3, 成功: 3, 失败: 0
2025-08-01 18:36:38.399 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad]
2025-08-01 18:36:38.399 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad]
2025-08-01 18:36:38.399 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7950eaad]
2025-08-01 18:36:38.406 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-1] c.e.p.c.openai.AiConfigController : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 1, 总数: 3, 成功: 3, 失败: 0
2025-08-01 18:36:38.414 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-08-01 18:36:38.427 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=批量添加完成, success=true, data=BatchAddApiKeyResult(successKeys=[ApiKeyDto(id=4 (truncated)...]
2025-08-01 18:36:38.439 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-08-01 18:36:38.440 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-01 18:39:38.008 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing POST /api/ai/config/generate-compatible-key?keyName=key666
2025-08-01 18:39:38.008 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-01 18:39:38.008 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#generateCompatibleKey(String, Authentication)
2025-08-01 18:39:38.091 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-08-01 18:39:38.094 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-08-01 18:39:38.094 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:39:38.094 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11a0a64e]
2025-08-01 18:39:38.095 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@227170278 wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will be managed by Spring
2025-08-01 18:39:38.095 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-08-01 18:39:38.095 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-08-01 18:39:38.097 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-08-01 18:39:38.098 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11a0a64e]
2025-08-01 18:39:38.098 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-08-01 18:39:38.098 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11a0a64e]
2025-08-01 18:39:38.098 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11a0a64e]
2025-08-01 18:39:38.098 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11a0a64e]
2025-08-01 18:39:38.101 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-08-01 18:39:38.102 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai/config/generate-compatible-key?keyName=key666] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-01 18:39:38.102 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured POST /api/ai/config/generate-compatible-key?keyName=key666
2025-08-01 18:39:38.103 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : POST "/api/ai/config/generate-compatible-key?keyName=key666", parameters={masked}
2025-08-01 18:39:38.104 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#generateCompatibleKey(String, Authentication)
2025-08-01 18:39:38.123 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:39:38.123 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1696d418]
2025-08-01 18:39:38.123 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@963063202 wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will be managed by Spring
2025-08-01 18:39:38.123 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] c.e.p.m.p.C.countByUserIdAndKeyName : ==>  Preparing: SELECT COUNT(*) FROM ai_compatible_api_keys WHERE user_id = ? AND key_name = ?
2025-08-01 18:39:38.124 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] c.e.p.m.p.C.countByUserIdAndKeyName : ==> Parameters: 1(Long), key666(String)
2025-08-01 18:39:38.127 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] c.e.p.m.p.C.countByUserIdAndKeyName : <==      Total: 1
2025-08-01 18:39:38.127 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1696d418]
2025-08-01 18:39:38.132 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] c.e.pure.util.SpringEncryptionUtil : 生成兼容API密钥成功 - 用户ID: 1, 密钥名称: key666, 密钥哈希: NhkAMdjtUarQLXBSwBQhvGFaeIskkFmVcDe626jXk
2025-08-01 18:39:38.132 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1696d418] from current transaction
2025-08-01 18:39:38.132 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] c.e.p.m.p.C.insert : ==>  Preparing: INSERT INTO ai_compatible_api_keys ( user_id, key_name, key_hash, salt, usage_count ) VALUES ( ?, ?, ?, ?, ? )
2025-08-01 18:39:38.132 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] c.e.p.m.p.C.insert : ==> Parameters: 1(Long), key666(String), NhkAMdjtUarQLXBSwBQhvGFaeIskkFmVcDe626jXk(String), be0de9a1ee75b0a9(String), 0(Long)
2025-08-01 18:39:38.136 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] c.e.p.m.p.C.insert : <==    Updates: 1
2025-08-01 18:39:38.136 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1696d418]
2025-08-01 18:39:38.136 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-7] c.e.p.s.o.i.CompatibleApiKeyServiceImpl : 创建兼容API密钥成功 - 用户ID: 1, 密钥名称: key666, 密钥ID: 2
2025-08-01 18:39:38.136 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1696d418]
2025-08-01 18:39:38.136 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1696d418]
2025-08-01 18:39:38.136 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1696d418]
2025-08-01 18:39:38.147 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-7] c.e.p.c.openai.AiConfigController : 生成兼容API密钥成功 - 用户ID: 1, 密钥名称: key666, 密钥ID: 2
2025-08-01 18:39:38.147 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-08-01 18:39:38.148 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=生成成功, success=true, data=com.example.pure.controller.openai.AiConfigControl (truncated)...]
2025-08-01 18:39:38.149 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-08-01 18:39:38.150 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-01 18:40:22.179 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing POST /v1/chat/completions
2025-08-01 18:40:22.179 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-01 18:40:22.180 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.OpenAiCompatibleController#chatCompletions(OpenAiChatRequest, HttpServletRequest)
2025-08-01 18:40:22.180 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-08-01 18:40:22.182 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /v1/chat/completions] with attributes [permitAll]
2025-08-01 18:40:22.182 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured POST /v1/chat/completions
2025-08-01 18:40:22.182 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : POST "/v1/chat/completions", parameters={}
2025-08-01 18:40:22.182 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.OpenAiCompatibleController#chatCompletions(OpenAiChatRequest, HttpServletRequest)
2025-08-01 18:40:22.193 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] c.e.pure.config.ContentDeserializer : 反序列化字符串格式内容: You are a helpful assistant
2025-08-01 18:40:22.194 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] c.e.pure.config.ContentDeserializer : 反序列化数组格式内容
2025-08-01 18:40:22.201 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [OpenAiChatRequest(model=gemini-2.5-pro, messages=[OpenAiChatRequest.OpenAiMessage(role=system, conte (truncated)...]
2025-08-01 18:40:22.223 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.w.c.r.async.WebAsyncManager : Started async request
2025-08-01 18:40:22.226 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.w.c.r.async.WebAsyncManager : Async result set, dispatch to /v1/chat/completions
2025-08-01 18:40:22.228 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Exiting but response remains open for further handling
2025-08-01 18:40:22.228 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-01 18:40:22.230 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing POST /v1/chat/completions
2025-08-01 18:40:22.230 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-01 18:40:22.230 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-08-01 18:40:22.230 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured POST /v1/chat/completions
2025-08-01 18:40:22.230 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : "ASYNC" dispatch for POST "/v1/chat/completions", parameters={}
2025-08-01 18:40:22.231 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-08-01 18:40:22.235 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-08-01 18:40:22.236 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Exiting from "ASYNC" dispatch, status 200
2025-08-01 18:40:22.236 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-01 18:40:37.779 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing POST /v1/chat/completions
2025-08-01 18:40:37.779 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-01 18:40:37.780 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.OpenAiCompatibleController#chatCompletions(OpenAiChatRequest, HttpServletRequest)
2025-08-01 18:40:37.780 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-08-01 18:40:37.780 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /v1/chat/completions] with attributes [permitAll]
2025-08-01 18:40:37.780 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured POST /v1/chat/completions
2025-08-01 18:40:37.780 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : POST "/v1/chat/completions", parameters={}
2025-08-01 18:40:37.781 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.OpenAiCompatibleController#chatCompletions(OpenAiChatRequest, HttpServletRequest)
2025-08-01 18:40:37.781 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.pure.config.ContentDeserializer : 反序列化字符串格式内容: You are a helpful assistant
2025-08-01 18:40:37.781 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.pure.config.ContentDeserializer : 反序列化数组格式内容
2025-08-01 18:40:37.781 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [OpenAiChatRequest(model=gemini-2.5-pro, messages=[OpenAiChatRequest.OpenAiMessage(role=system, conte (truncated)...]
2025-08-01 18:40:37.783 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.OpenAiRequestServiceImpl : 收到OpenAI兼容聊天请求 - 模型: gemini-2.5-pro, 流式: true, 消息数: 2
2025-08-01 18:40:37.783 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.OpenAiRequestServiceImpl : 处理流式聊天请求 - 模型: gemini-2.5-pro
2025-08-01 18:40:37.784 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 处理OpenAI兼容流式聊天请求 - 模型: gemini-2.5-pro
2025-08-01 18:40:37.784 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:40:37.784 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6842d9ff] was not registered for synchronization because synchronization is not active
2025-08-01 18:40:37.785 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1995423855 wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will not be managed by Spring
2025-08-01 18:40:37.786 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.C.selectByKeyHash : ==>  Preparing: SELECT id, user_id, key_name, key_hash, salt, usage_count, last_used_at, created_at, updated_at FROM ai_compatible_api_keys WHERE key_hash = ?
2025-08-01 18:40:37.786 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.C.selectByKeyHash : ==> Parameters: NhkAMdjtUarQLXBSwBQhvGFaeIskkFmVcDe626jXk(String)
2025-08-01 18:40:37.788 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.C.selectByKeyHash : <==      Total: 1
2025-08-01 18:40:37.788 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6842d9ff]
2025-08-01 18:40:37.791 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.pure.util.SpringEncryptionUtil : 解析兼容API密钥成功 - 用户ID: 1, 密钥名称: key666
2025-08-01 18:40:37.792 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:40:37.792 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@736a3c88] was not registered for synchronization because synchronization is not active
2025-08-01 18:40:37.792 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1733938235 wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will not be managed by Spring
2025-08-01 18:40:37.792 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.C.updateUsageStatsByKeyHash : ==>  Preparing: UPDATE ai_compatible_api_keys SET usage_count = usage_count + 1, last_used_at = NOW(), updated_at = NOW() WHERE key_hash = ?
2025-08-01 18:40:37.792 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.C.updateUsageStatsByKeyHash : ==> Parameters: NhkAMdjtUarQLXBSwBQhvGFaeIskkFmVcDe626jXk(String)
2025-08-01 18:40:37.811 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.C.updateUsageStatsByKeyHash : <==    Updates: 1
2025-08-01 18:40:37.811 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@736a3c88]
2025-08-01 18:40:37.811 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.CompatibleApiKeyServiceImpl : 兼容密钥验证成功（新格式） - 用户ID: 1
2025-08-01 18:40:37.811 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 开始异步处理流式聊天 - 兼容密钥: sk-w4EfD9GC4pEpVP17h...
2025-08-01 18:40:37.811 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.impl.AiConfigServiceImpl : 获取用户AI配置分组列表 - 用户ID: 1
2025-08-01 18:40:37.811 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:40:37.811 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64a9d7fe] was not registered for synchronization because synchronization is not active
2025-08-01 18:40:37.812 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@150500087 wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will not be managed by Spring
2025-08-01 18:40:37.812 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.U.selectByUserId : ==>  Preparing: SELECT id, user_id, group_name, provider, custom_base_url, test_model, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt, created_at, updated_at FROM user_ai_config_groups WHERE user_id = ? ORDER BY created_at ASC
2025-08-01 18:40:37.812 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.U.selectByUserId : ==> Parameters: 1(Long)
2025-08-01 18:40:37.814 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.U.selectByUserId : <==      Total: 1
2025-08-01 18:40:37.814 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64a9d7fe]
2025-08-01 18:40:37.815 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.LoadBalancerServiceImpl : 选择最佳API密钥 - 用户ID: 1, 提供商: GOOGLE
2025-08-01 18:40:37.815 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:40:37.815 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@49b4e6a8] was not registered for synchronization because synchronization is not active
2025-08-01 18:40:37.815 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will not be managed by Spring
2025-08-01 18:40:37.815 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.U.selectActiveByUserIdAndProvider : ==>  Preparing: SELECT k.id, k.user_id, k.config_group_id, k.api_key_encrypted, k.is_active, k.priority, k.usage_count, k.last_used_at, k.created_at, k.updated_at, g.provider FROM user_api_keys k LEFT JOIN user_ai_config_groups g ON k.config_group_id = g.id WHERE k.user_id = ? AND g.provider = ? AND k.is_active = TRUE ORDER BY k.priority ASC, k.created_at ASC
2025-08-01 18:40:37.815 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.U.selectActiveByUserIdAndProvider : ==> Parameters: 1(Long), GOOGLE(String)
2025-08-01 18:40:37.820 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.U.selectActiveByUserIdAndProvider : <==      Total: 3
2025-08-01 18:40:37.820 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@49b4e6a8]
2025-08-01 18:40:37.820 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:40:37.820 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b13893d] was not registered for synchronization because synchronization is not active
2025-08-01 18:40:37.820 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@30462979 wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will not be managed by Spring
2025-08-01 18:40:37.820 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.selectHealthyByUserIdAndProvider : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE user_id = ? AND provider = ? AND is_healthy = TRUE ORDER BY current_requests ASC, total_requests ASC
2025-08-01 18:40:37.820 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.selectHealthyByUserIdAndProvider : ==> Parameters: 1(Long), GOOGLE(String)
2025-08-01 18:40:37.823 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.selectHealthyByUserIdAndProvider : <==      Total: 0
2025-08-01 18:40:37.823 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b13893d]
2025-08-01 18:40:37.823 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化用户1的GOOGLE提供商API密钥负载状态
2025-08-01 18:40:37.824 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态 - ID: 4
2025-08-01 18:40:37.824 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:40:37.824 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51eb3040] was not registered for synchronization because synchronization is not active
2025-08-01 18:40:37.824 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will not be managed by Spring
2025-08-01 18:40:37.824 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-01 18:40:37.824 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 4(Long)
2025-08-01 18:40:37.826 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 0
2025-08-01 18:40:37.826 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51eb3040]
2025-08-01 18:40:37.827 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:40:37.827 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@756e1b76] was not registered for synchronization because synchronization is not active
2025-08-01 18:40:37.827 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will not be managed by Spring
2025-08-01 18:40:37.827 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.insert : ==>  Preparing: INSERT INTO api_key_load_balance ( user_id, provider, api_key_id, current_requests, total_requests, error_count, is_healthy ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-08-01 18:40:37.827 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@756e1b76]
2025-08-01 18:40:37.828 [1;31mERROR[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态时发生错误 - ID: 4
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'provider' in 'class com.example.pure.model.entity.ApiKeyLoadBalance'
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy109.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:142)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy2/jdk.proxy2.$Proxy149.insert(Unknown Source)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl.initializeLoadBalanceState(LoadBalancerServiceImpl.java:317)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl.selectBestApiKey(LoadBalancerServiceImpl.java:51)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl$$FastClassBySpringCGLIB$$36b9f669.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl$$EnhancerBySpringCGLIB$$c1f6b0bd.selectBestApiKey(<generated>)
	at com.example.pure.service.openai.impl.CompatibleApiKeyServiceImpl.selectApiKeyForModel(CompatibleApiKeyServiceImpl.java:281)
	at com.example.pure.service.openai.impl.CompatibleApiKeyServiceImpl$$FastClassBySpringCGLIB$$d55508c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.example.pure.service.openai.impl.CompatibleApiKeyServiceImpl$$EnhancerBySpringCGLIB$$1af4ad06.selectApiKeyForModel(<generated>)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.processStreamChatAsync(OpenAiCompatibleServiceImpl.java:445)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.streamChatCompletions(OpenAiCompatibleServiceImpl.java:115)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at jdk.proxy2/jdk.proxy2.$Proxy151.streamChatCompletions(Unknown Source)
	at com.example.pure.service.openai.impl.OpenAiRequestServiceImpl.processChatCompletions(OpenAiRequestServiceImpl.java:50)
	at com.example.pure.controller.openai.OpenAiCompatibleController.chatCompletions(OpenAiCompatibleController.java:103)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$FastClassBySpringCGLIB$$9755d011.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$EnhancerBySpringCGLIB$$f11d1235.chatCompletions(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'provider' in 'class com.example.pure.model.entity.ApiKeyLoadBalance'
	at org.apache.ibatis.reflection.Reflector.getGetInvoker(Reflector.java:387)
	at org.apache.ibatis.reflection.MetaClass.getGetInvoker(MetaClass.java:162)
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:159)
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49)
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:116)
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:79)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.parameterize(PreparedStatementHandler.java:97)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.parameterize(RoutingStatementHandler.java:65)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:91)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 145 common frames omitted
2025-08-01 18:40:37.828 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态 - ID: 5
2025-08-01 18:40:37.828 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:40:37.828 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@74d0f5e] was not registered for synchronization because synchronization is not active
2025-08-01 18:40:37.828 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2121992 wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will not be managed by Spring
2025-08-01 18:40:37.828 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-01 18:40:37.829 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 5(Long)
2025-08-01 18:40:37.830 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 0
2025-08-01 18:40:37.831 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@74d0f5e]
2025-08-01 18:40:37.831 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:40:37.831 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@570eb583] was not registered for synchronization because synchronization is not active
2025-08-01 18:40:37.831 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@953579459 wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will not be managed by Spring
2025-08-01 18:40:37.831 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.insert : ==>  Preparing: INSERT INTO api_key_load_balance ( user_id, provider, api_key_id, current_requests, total_requests, error_count, is_healthy ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-08-01 18:40:37.831 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@570eb583]
2025-08-01 18:40:37.832 [1;31mERROR[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态时发生错误 - ID: 5
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'provider' in 'class com.example.pure.model.entity.ApiKeyLoadBalance'
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy109.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:142)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy2/jdk.proxy2.$Proxy149.insert(Unknown Source)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl.initializeLoadBalanceState(LoadBalancerServiceImpl.java:317)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl.selectBestApiKey(LoadBalancerServiceImpl.java:51)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl$$FastClassBySpringCGLIB$$36b9f669.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl$$EnhancerBySpringCGLIB$$c1f6b0bd.selectBestApiKey(<generated>)
	at com.example.pure.service.openai.impl.CompatibleApiKeyServiceImpl.selectApiKeyForModel(CompatibleApiKeyServiceImpl.java:281)
	at com.example.pure.service.openai.impl.CompatibleApiKeyServiceImpl$$FastClassBySpringCGLIB$$d55508c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.example.pure.service.openai.impl.CompatibleApiKeyServiceImpl$$EnhancerBySpringCGLIB$$1af4ad06.selectApiKeyForModel(<generated>)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.processStreamChatAsync(OpenAiCompatibleServiceImpl.java:445)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.streamChatCompletions(OpenAiCompatibleServiceImpl.java:115)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at jdk.proxy2/jdk.proxy2.$Proxy151.streamChatCompletions(Unknown Source)
	at com.example.pure.service.openai.impl.OpenAiRequestServiceImpl.processChatCompletions(OpenAiRequestServiceImpl.java:50)
	at com.example.pure.controller.openai.OpenAiCompatibleController.chatCompletions(OpenAiCompatibleController.java:103)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$FastClassBySpringCGLIB$$9755d011.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$EnhancerBySpringCGLIB$$f11d1235.chatCompletions(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'provider' in 'class com.example.pure.model.entity.ApiKeyLoadBalance'
	at org.apache.ibatis.reflection.Reflector.getGetInvoker(Reflector.java:387)
	at org.apache.ibatis.reflection.MetaClass.getGetInvoker(MetaClass.java:162)
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:159)
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49)
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:116)
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:79)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.parameterize(PreparedStatementHandler.java:97)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.parameterize(RoutingStatementHandler.java:65)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:91)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 145 common frames omitted
2025-08-01 18:40:37.832 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态 - ID: 6
2025-08-01 18:40:37.832 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:40:37.832 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19ea2110] was not registered for synchronization because synchronization is not active
2025-08-01 18:40:37.832 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@310982017 wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will not be managed by Spring
2025-08-01 18:40:37.832 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-01 18:40:37.832 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 6(Long)
2025-08-01 18:40:37.835 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 0
2025-08-01 18:40:37.836 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19ea2110]
2025-08-01 18:40:37.836 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:40:37.836 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2821f576] was not registered for synchronization because synchronization is not active
2025-08-01 18:40:37.836 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will not be managed by Spring
2025-08-01 18:40:37.836 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.insert : ==>  Preparing: INSERT INTO api_key_load_balance ( user_id, provider, api_key_id, current_requests, total_requests, error_count, is_healthy ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-08-01 18:40:37.836 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2821f576]
2025-08-01 18:40:37.837 [1;31mERROR[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态时发生错误 - ID: 6
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'provider' in 'class com.example.pure.model.entity.ApiKeyLoadBalance'
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy109.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:142)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy2/jdk.proxy2.$Proxy149.insert(Unknown Source)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl.initializeLoadBalanceState(LoadBalancerServiceImpl.java:317)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl.selectBestApiKey(LoadBalancerServiceImpl.java:51)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl$$FastClassBySpringCGLIB$$36b9f669.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.example.pure.service.openai.impl.LoadBalancerServiceImpl$$EnhancerBySpringCGLIB$$c1f6b0bd.selectBestApiKey(<generated>)
	at com.example.pure.service.openai.impl.CompatibleApiKeyServiceImpl.selectApiKeyForModel(CompatibleApiKeyServiceImpl.java:281)
	at com.example.pure.service.openai.impl.CompatibleApiKeyServiceImpl$$FastClassBySpringCGLIB$$d55508c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.example.pure.service.openai.impl.CompatibleApiKeyServiceImpl$$EnhancerBySpringCGLIB$$1af4ad06.selectApiKeyForModel(<generated>)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.processStreamChatAsync(OpenAiCompatibleServiceImpl.java:445)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.streamChatCompletions(OpenAiCompatibleServiceImpl.java:115)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at jdk.proxy2/jdk.proxy2.$Proxy151.streamChatCompletions(Unknown Source)
	at com.example.pure.service.openai.impl.OpenAiRequestServiceImpl.processChatCompletions(OpenAiRequestServiceImpl.java:50)
	at com.example.pure.controller.openai.OpenAiCompatibleController.chatCompletions(OpenAiCompatibleController.java:103)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$FastClassBySpringCGLIB$$9755d011.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$EnhancerBySpringCGLIB$$f11d1235.chatCompletions(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'provider' in 'class com.example.pure.model.entity.ApiKeyLoadBalance'
	at org.apache.ibatis.reflection.Reflector.getGetInvoker(Reflector.java:387)
	at org.apache.ibatis.reflection.MetaClass.getGetInvoker(MetaClass.java:162)
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:159)
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49)
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:116)
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:79)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.parameterize(PreparedStatementHandler.java:97)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.parameterize(RoutingStatementHandler.java:65)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:91)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 145 common frames omitted
2025-08-01 18:40:37.837 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:40:37.837 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1484282] was not registered for synchronization because synchronization is not active
2025-08-01 18:40:37.837 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will not be managed by Spring
2025-08-01 18:40:37.837 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.selectHealthyByUserIdAndProvider : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE user_id = ? AND provider = ? AND is_healthy = TRUE ORDER BY current_requests ASC, total_requests ASC
2025-08-01 18:40:37.838 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.selectHealthyByUserIdAndProvider : ==> Parameters: 1(Long), GOOGLE(String)
2025-08-01 18:40:37.840 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.selectHealthyByUserIdAndProvider : <==      Total: 0
2025-08-01 18:40:37.840 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1484282]
2025-08-01 18:40:37.840 [31mWARN [0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.LoadBalancerServiceImpl : 未找到最佳API密钥，返回第一个活跃密钥
2025-08-01 18:40:37.840 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.CompatibleApiKeyServiceImpl : 为用户1选择API密钥成功 - 密钥ID: 4, 提供商: GOOGLE, 模型: gemini-2.5-pro
2025-08-01 18:40:37.841 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.pure.util.MultimodalContentUtils : 纯文本消息 - 长度: 27 字符
2025-08-01 18:40:37.841 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.pure.util.MultimodalContentUtils : 纯文本消息 - 长度: 15 字符
2025-08-01 18:40:37.842 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.LoadBalancerServiceImpl : 开始使用API密钥 - ID: 4
2025-08-01 18:40:37.842 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:40:37.842 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@74642548]
2025-08-01 18:40:37.842 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@620701572 wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will be managed by Spring
2025-08-01 18:40:37.842 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.incrementRequests : ==>  Preparing: UPDATE api_key_load_balance SET current_requests = current_requests + 1, total_requests = total_requests + 1, updated_at = NOW() WHERE api_key_id = ?
2025-08-01 18:40:37.842 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.incrementRequests : ==> Parameters: 4(Long)
2025-08-01 18:40:37.845 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.m.p.A.incrementRequests : <==    Updates: 0
2025-08-01 18:40:37.845 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@74642548]
2025-08-01 18:40:37.845 [31mWARN [0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.LoadBalancerServiceImpl : 更新API密钥请求计数失败 - ID: 4
2025-08-01 18:40:37.845 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@74642548]
2025-08-01 18:40:37.845 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@74642548]
2025-08-01 18:40:37.845 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@74642548]
2025-08-01 18:40:37.850 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送SSE事件 - 响应ID: chatcmpl-894c5272, 数据大小: 232 bytes
2025-08-01 18:40:37.850 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-pro
2025-08-01 18:40:37.850 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.ModelAdapterServiceImpl : 为提供商 GOOGLE 添加推理参数
2025-08-01 18:40:37.850 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.ModelAdapterServiceImpl : 为GOOGLE添加stream_options参数
2025-08-01 18:40:37.850 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 流式请求详情 ===
2025-08-01 18:40:37.850 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-01 18:40:37.850 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyBRV9...
2025-08-01 18:40:37.850 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {top_p=1.0, reasoning_effort=medium, stream=true, max_tokens=6000, temperature=0.8, messages=[{role=system, content=You are a helpful assistant}, {role=user, content=请你生成一个java的简单代码}], model=gemini-2.5-pro, stream_options=OpenAiChatRequest.StreamOptions(includeUsage=true)}
2025-08-01 18:40:37.857 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.ModelAdapterServiceImpl : Google流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-01 18:40:37.858 [34mINFO [0;39m 11336 --- [http-nio-8080-exec-10] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 开始流式聊天 - 响应ID: chatcmpl-894c5272, 提供商: GOOGLE, 模型: gemini-2.5-pro
2025-08-01 18:40:37.858 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.s.w.r.f.client.ExchangeFunctions : [35685621] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-01 18:40:37.862 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.s.w.c.r.async.WebAsyncManager : Started async request
2025-08-01 18:40:37.862 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Exiting but response remains open for further handling
2025-08-01 18:40:37.863 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-01 18:40:38.062 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] org.springframework.web.HttpLogging : [35685621] Encoding [{top_p=1.0, reasoning_effort=medium, stream=true, max_tokens=6000, temperature=0.8, messages=[{role= (truncated)...]
2025-08-01 18:40:57.318 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] o.s.w.r.f.client.ExchangeFunctions : [35685621] [ca91d7c4-1] Response 200 OK
2025-08-01 18:40:57.327 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"当然！这里为您提供几个不同复杂程度的 Java","role":"assistant"},"index":0}],"created":1754044856,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":11,"prompt_tokens":15,"total_tokens":1758}}
2025-08-01 18:40:57.327 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"当然！这里为您提供几个不同复杂程度的 Java","role":"assistant"},"index":0}],"created":1754044856,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":11,"prompt_tokens":15,"total_tokens":1758}}
2025-08-01 18:40:57.327 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 272, 响应ID: chatcmpl-894c5272
2025-08-01 18:40:57.327 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 272 bytes
2025-08-01 18:40:57.541 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" 简单代码示例，从最基础的“Hello, World!”到稍微复杂一点的交互式程序和简单的面向","role":"assistant"},"index":0}],"created":1754044856,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":35,"prompt_tokens":15,"total_tokens":1782}}
2025-08-01 18:40:57.541 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" 简单代码示例，从最基础的“Hello, World!”到稍微复杂一点的交互式程序和简单的面向","role":"assistant"},"index":0}],"created":1754044856,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":35,"prompt_tokens":15,"total_tokens":1782}}
2025-08-01 18:40:57.541 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 296, 响应ID: chatcmpl-894c5272
2025-08-01 18:40:57.541 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 296 bytes
2025-08-01 18:40:57.810 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"对象编程例子。\n\n### 示例 1: 经典的 \"Hello, World!\" (最简单)\n这是","role":"assistant"},"index":0}],"created":1754044856,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":59,"prompt_tokens":15,"total_tokens":1806}}
2025-08-01 18:40:57.810 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"对象编程例子。\n\n### 示例 1: 经典的 \"Hello, World!\" (最简单)\n这是","role":"assistant"},"index":0}],"created":1754044856,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":59,"prompt_tokens":15,"total_tokens":1806}}
2025-08-01 18:40:57.810 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 301, 响应ID: chatcmpl-894c5272
2025-08-01 18:40:57.810 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 301 bytes
2025-08-01 18:40:58.045 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"学习任何编程语言的第一个程序，它只做一件事：在屏幕上打印 \"Hello, World!\"。\n\n**代码","role":"assistant"},"index":0}],"created":1754044856,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":86,"prompt_tokens":15,"total_tokens":1833}}
2025-08-01 18:40:58.045 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"学习任何编程语言的第一个程序，它只做一件事：在屏幕上打印 \"Hello, World!\"。\n\n**代码","role":"assistant"},"index":0}],"created":1754044856,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":86,"prompt_tokens":15,"total_tokens":1833}}
2025-08-01 18:40:58.045 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 304, 响应ID: chatcmpl-894c5272
2025-08-01 18:40:58.045 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 304 bytes
2025-08-01 18:40:58.358 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" (`HelloWorld.java`):**\n```java\n// 定义一个名为 HelloWorld 的公共类\npublic class HelloWorld {\n    ","role":"assistant"},"index":0}],"created":1754044857,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":110,"prompt_tokens":15,"total_tokens":1857}}
2025-08-01 18:40:58.358 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" (`HelloWorld.java`):**\n```java\n// 定义一个名为 HelloWorld 的公共类\npublic class HelloWorld {\n    ","role":"assistant"},"index":0}],"created":1754044857,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":110,"prompt_tokens":15,"total_tokens":1857}}
2025-08-01 18:40:58.358 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 342, 响应ID: chatcmpl-894c5272
2025-08-01 18:40:58.358 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 342 bytes
2025-08-01 18:40:58.680 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n    // 这是程序的入口点 -> main 方法\n    // JVM (Java虚拟机) 从这里开始执行代码\n    public static","role":"assistant"},"index":0}],"created":1754044857,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":135,"prompt_tokens":15,"total_tokens":1882}}
2025-08-01 18:40:58.680 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n    // 这是程序的入口点 -> main 方法\n    // JVM (Java虚拟机) 从这里开始执行代码\n    public static","role":"assistant"},"index":0}],"created":1754044857,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":135,"prompt_tokens":15,"total_tokens":1882}}
2025-08-01 18:40:58.680 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 329, 响应ID: chatcmpl-894c5272
2025-08-01 18:40:58.680 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 329 bytes
2025-08-01 18:40:58.880 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" void main(String[] args) {\n        \n        // 使用 System.out.println() 在控制台输出","role":"assistant"},"index":0}],"created":1754044857,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":159,"prompt_tokens":15,"total_tokens":1906}}
2025-08-01 18:40:58.880 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" void main(String[] args) {\n        \n        // 使用 System.out.println() 在控制台输出","role":"assistant"},"index":0}],"created":1754044857,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":159,"prompt_tokens":15,"total_tokens":1906}}
2025-08-01 18:40:58.880 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 330, 响应ID: chatcmpl-894c5272
2025-08-01 18:40:58.880 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 330 bytes
2025-08-01 18:40:59.130 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"一行文本\n        // println 会在输出后自动换行\n        System.out.println(\"Hello, World!\");","role":"assistant"},"index":0}],"created":1754044857,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":184,"prompt_tokens":15,"total_tokens":1931}}
2025-08-01 18:40:59.130 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"一行文本\n        // println 会在输出后自动换行\n        System.out.println(\"Hello, World!\");","role":"assistant"},"index":0}],"created":1754044857,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":184,"prompt_tokens":15,"total_tokens":1931}}
2025-08-01 18:40:59.130 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 332, 响应ID: chatcmpl-894c5272
2025-08-01 18:40:59.130 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 332 bytes
2025-08-01 18:40:59.314 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n    }\n}\n```\n\n**如何运行:**\n1.  将代码保存为 `HelloWorld.java","role":"assistant"},"index":0}],"created":1754044858,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":206,"prompt_tokens":15,"total_tokens":1953}}
2025-08-01 18:40:59.314 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n    }\n}\n```\n\n**如何运行:**\n1.  将代码保存为 `HelloWorld.java","role":"assistant"},"index":0}],"created":1754044858,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":206,"prompt_tokens":15,"total_tokens":1953}}
2025-08-01 18:40:59.314 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 307, 响应ID: chatcmpl-894c5272
2025-08-01 18:40:59.314 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 307 bytes
2025-08-01 18:40:59.576 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"` 文件（文件名必须和类名 `HelloWorld` 完全一样）。\n2.  打开终端或命令提示符。\n3","role":"assistant"},"index":0}],"created":1754044858,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":233,"prompt_tokens":15,"total_tokens":1980}}
2025-08-01 18:40:59.576 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"` 文件（文件名必须和类名 `HelloWorld` 完全一样）。\n2.  打开终端或命令提示符。\n3","role":"assistant"},"index":0}],"created":1754044858,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":233,"prompt_tokens":15,"total_tokens":1980}}
2025-08-01 18:40:59.576 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 303, 响应ID: chatcmpl-894c5272
2025-08-01 18:40:59.576 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 303 bytes
2025-08-01 18:40:59.796 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":".  使用 `javac` 命令编译代码：`javac HelloWorld.java`\n4.  如果没有错误，会生成","role":"assistant"},"index":0}],"created":1754044858,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":258,"prompt_tokens":15,"total_tokens":2005}}
2025-08-01 18:40:59.796 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":".  使用 `javac` 命令编译代码：`javac HelloWorld.java`\n4.  如果没有错误，会生成","role":"assistant"},"index":0}],"created":1754044858,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":258,"prompt_tokens":15,"total_tokens":2005}}
2025-08-01 18:40:59.796 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 310, 响应ID: chatcmpl-894c5272
2025-08-01 18:40:59.796 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 310 bytes
2025-08-01 18:40:59.979 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"一个 `HelloWorld.class` 文件。\n5.  使用 `java` 命令运行程序：`java HelloWorld`","role":"assistant"},"index":0}],"created":1754044858,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":282,"prompt_tokens":15,"total_tokens":2029}}
2025-08-01 18:40:59.979 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"一个 `HelloWorld.class` 文件。\n5.  使用 `java` 命令运行程序：`java HelloWorld`","role":"assistant"},"index":0}],"created":1754044858,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":282,"prompt_tokens":15,"total_tokens":2029}}
2025-08-01 18:40:59.979 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 315, 响应ID: chatcmpl-894c5272
2025-08-01 18:40:59.979 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 315 bytes
2025-08-01 18:41:00.201 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n\n**预期输出:**\n```\nHello, World!\n```\n\n---\n\n### 示例 2: 用户输入与变量","role":"assistant"},"index":0}],"created":1754044858,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":308,"prompt_tokens":15,"total_tokens":2055}}
2025-08-01 18:41:00.201 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n\n**预期输出:**\n```\nHello, World!\n```\n\n---\n\n### 示例 2: 用户输入与变量","role":"assistant"},"index":0}],"created":1754044858,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":308,"prompt_tokens":15,"total_tokens":2055}}
2025-08-01 18:41:00.201 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 316, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:00.201 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 316 bytes
2025-08-01 18:41:00.548 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" (交互式)\n这个例子会向用户提问，读取用户的输入，然后根据输入内容给出一个回应。它","role":"assistant"},"index":0}],"created":1754044859,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":334,"prompt_tokens":15,"total_tokens":2081}}
2025-08-01 18:41:00.548 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" (交互式)\n这个例子会向用户提问，读取用户的输入，然后根据输入内容给出一个回应。它","role":"assistant"},"index":0}],"created":1754044859,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":334,"prompt_tokens":15,"total_tokens":2081}}
2025-08-01 18:41:00.548 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 293, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:00.548 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 293 bytes
2025-08-01 18:41:00.794 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"演示了变量和用户输入。\n\n**代码 (`Greeter.java`):**\n```java\n// 需要","role":"assistant"},"index":0}],"created":1754044859,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":358,"prompt_tokens":15,"total_tokens":2105}}
2025-08-01 18:41:00.794 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"演示了变量和用户输入。\n\n**代码 (`Greeter.java`):**\n```java\n// 需要","role":"assistant"},"index":0}],"created":1754044859,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":358,"prompt_tokens":15,"total_tokens":2105}}
2025-08-01 18:41:00.794 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 305, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:00.794 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 305 bytes
2025-08-01 18:41:01.051 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"导入 Scanner 类来读取用户输入\nimport java.util.Scanner;\n\n// 定义一个名为 Greeter 的公共类\n","role":"assistant"},"index":0}],"created":1754044859,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":383,"prompt_tokens":15,"total_tokens":2130}}
2025-08-01 18:41:01.051 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"导入 Scanner 类来读取用户输入\nimport java.util.Scanner;\n\n// 定义一个名为 Greeter 的公共类\n","role":"assistant"},"index":0}],"created":1754044859,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":383,"prompt_tokens":15,"total_tokens":2130}}
2025-08-01 18:41:01.051 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 324, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:01.051 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 324 bytes
2025-08-01 18:41:01.278 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"public class Greeter {\n\n    public static void main(String[] args) {\n        // 创建一个 Scanner 对象来读取","role":"assistant"},"index":0}],"created":1754044860,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":409,"prompt_tokens":15,"total_tokens":2156}}
2025-08-01 18:41:01.278 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"public class Greeter {\n\n    public static void main(String[] args) {\n        // 创建一个 Scanner 对象来读取","role":"assistant"},"index":0}],"created":1754044860,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":409,"prompt_tokens":15,"total_tokens":2156}}
2025-08-01 18:41:01.278 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 351, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:01.278 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 351 bytes
2025-08-01 18:41:01.476 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"标准输入（键盘）\n        Scanner scanner = new Scanner(System.in);\n        \n        // 提示用户输入名字","role":"assistant"},"index":0}],"created":1754044860,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":436,"prompt_tokens":15,"total_tokens":2183}}
2025-08-01 18:41:01.476 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"标准输入（键盘）\n        Scanner scanner = new Scanner(System.in);\n        \n        // 提示用户输入名字","role":"assistant"},"index":0}],"created":1754044860,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":436,"prompt_tokens":15,"total_tokens":2183}}
2025-08-01 18:41:01.476 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 340, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:01.476 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 340 bytes
2025-08-01 18:41:01.723 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n        System.out.print(\"你好！请输入你的名字: \");\n        \n        // 读取用户输入的一行文本","role":"assistant"},"index":0}],"created":1754044860,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":460,"prompt_tokens":15,"total_tokens":2207}}
2025-08-01 18:41:01.723 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n        System.out.print(\"你好！请输入你的名字: \");\n        \n        // 读取用户输入的一行文本","role":"assistant"},"index":0}],"created":1754044860,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":460,"prompt_tokens":15,"total_tokens":2207}}
2025-08-01 18:41:01.723 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 329, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:01.723 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 329 bytes
2025-08-01 18:41:01.966 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"，并将其存储在名为 name 的字符串变量中\n        String name = scanner.nextLine();\n        \n        //","role":"assistant"},"index":0}],"created":1754044860,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":485,"prompt_tokens":15,"total_tokens":2232}}
2025-08-01 18:41:01.966 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"，并将其存储在名为 name 的字符串变量中\n        String name = scanner.nextLine();\n        \n        //","role":"assistant"},"index":0}],"created":1754044860,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":485,"prompt_tokens":15,"total_tokens":2232}}
2025-08-01 18:41:01.966 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 337, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:01.966 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 337 bytes
2025-08-01 18:41:02.198 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" 提示用户输入年龄\n        System.out.print(\"请输入你的年龄: \");\n\n        // 读取用户输入的整数，","role":"assistant"},"index":0}],"created":1754044860,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":510,"prompt_tokens":15,"total_tokens":2257}}
2025-08-01 18:41:02.198 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" 提示用户输入年龄\n        System.out.print(\"请输入你的年龄: \");\n\n        // 读取用户输入的整数，","role":"assistant"},"index":0}],"created":1754044860,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":510,"prompt_tokens":15,"total_tokens":2257}}
2025-08-01 18:41:02.198 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 326, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:02.198 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 326 bytes
2025-08-01 18:41:02.430 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"并存储在名为 age 的整型变量中\n        int age = scanner.nextInt();\n        \n        //","role":"assistant"},"index":0}],"created":1754044861,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":534,"prompt_tokens":15,"total_tokens":2281}}
2025-08-01 18:41:02.430 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"并存储在名为 age 的整型变量中\n        int age = scanner.nextInt();\n        \n        //","role":"assistant"},"index":0}],"created":1754044861,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":534,"prompt_tokens":15,"total_tokens":2281}}
2025-08-01 18:41:02.430 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 327, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:02.430 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 327 bytes
2025-08-01 18:41:02.713 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" 输出一句欢迎语，包含用户的名字和年龄\n        System.out.println(\"欢迎你, \" + name +","role":"assistant"},"index":0}],"created":1754044861,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":559,"prompt_tokens":15,"total_tokens":2306}}
2025-08-01 18:41:02.714 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" 输出一句欢迎语，包含用户的名字和年龄\n        System.out.println(\"欢迎你, \" + name +","role":"assistant"},"index":0}],"created":1754044861,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":559,"prompt_tokens":15,"total_tokens":2306}}
2025-08-01 18:41:02.714 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 316, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:02.714 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 316 bytes
2025-08-01 18:41:02.991 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" \"! 你今年 \" + age + \" 岁了。\");\n        \n        // 关闭 scanner 对象，释放资源","role":"assistant"},"index":0}],"created":1754044861,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":583,"prompt_tokens":15,"total_tokens":2330}}
2025-08-01 18:41:02.991 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" \"! 你今年 \" + age + \" 岁了。\");\n        \n        // 关闭 scanner 对象，释放资源","role":"assistant"},"index":0}],"created":1754044861,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":583,"prompt_tokens":15,"total_tokens":2330}}
2025-08-01 18:41:02.991 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 321, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:02.991 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 321 bytes
2025-08-01 18:41:03.185 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"，这是一个好习惯\n        scanner.close();\n    }\n}\n```\n\n**如何运行:**\n1.","role":"assistant"},"index":0}],"created":1754044861,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":608,"prompt_tokens":15,"total_tokens":2355}}
2025-08-01 18:41:03.185 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"，这是一个好习惯\n        scanner.close();\n    }\n}\n```\n\n**如何运行:**\n1.","role":"assistant"},"index":0}],"created":1754044861,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":608,"prompt_tokens":15,"total_tokens":2355}}
2025-08-01 18:41:03.185 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 316, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:03.185 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 316 bytes
2025-08-01 18:41:03.441 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"  将代码保存为 `Greeter.java` 文件。\n2.  编译: `javac Greeter.java`","role":"assistant"},"index":0}],"created":1754044862,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":633,"prompt_tokens":15,"total_tokens":2380}}
2025-08-01 18:41:03.441 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"  将代码保存为 `Greeter.java` 文件。\n2.  编译: `javac Greeter.java`","role":"assistant"},"index":0}],"created":1754044862,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":633,"prompt_tokens":15,"total_tokens":2380}}
2025-08-01 18:41:03.441 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 307, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:03.441 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 307 bytes
2025-08-01 18:41:03.671 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n3.  运行: `java Greeter`\n4.  程序会提示你输入名字和年龄，","role":"assistant"},"index":0}],"created":1754044862,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":656,"prompt_tokens":15,"total_tokens":2403}}
2025-08-01 18:41:03.671 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n3.  运行: `java Greeter`\n4.  程序会提示你输入名字和年龄，","role":"assistant"},"index":0}],"created":1754044862,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":656,"prompt_tokens":15,"total_tokens":2403}}
2025-08-01 18:41:03.671 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 294, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:03.671 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 294 bytes
2025-08-01 18:41:03.891 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"按要求输入并回车。\n\n**预期输出 (交互过程):**\n```\n你好！请输入你的名字: 张","role":"assistant"},"index":0}],"created":1754044862,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":682,"prompt_tokens":15,"total_tokens":2429}}
2025-08-01 18:41:03.891 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"按要求输入并回车。\n\n**预期输出 (交互过程):**\n```\n你好！请输入你的名字: 张","role":"assistant"},"index":0}],"created":1754044862,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":682,"prompt_tokens":15,"total_tokens":2429}}
2025-08-01 18:41:03.891 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 299, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:03.891 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 299 bytes
2025-08-01 18:41:04.098 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"三\n请输入你的年龄: 25\n欢迎你, 张三! 你今年 25 岁了。\n```","role":"assistant"},"index":0}],"created":1754044862,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":709,"prompt_tokens":15,"total_tokens":2456}}
2025-08-01 18:41:04.098 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"三\n请输入你的年龄: 25\n欢迎你, 张三! 你今年 25 岁了。\n```","role":"assistant"},"index":0}],"created":1754044862,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":709,"prompt_tokens":15,"total_tokens":2456}}
2025-08-01 18:41:04.098 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 290, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:04.098 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 290 bytes
2025-08-01 18:41:04.307 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n\n---\n\n### 示例 3: 简单的面向对象编程 (OOP)\n这个例子展示了Java的核心","role":"assistant"},"index":0}],"created":1754044863,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":732,"prompt_tokens":15,"total_tokens":2479}}
2025-08-01 18:41:04.307 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n\n---\n\n### 示例 3: 简单的面向对象编程 (OOP)\n这个例子展示了Java的核心","role":"assistant"},"index":0}],"created":1754044863,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":732,"prompt_tokens":15,"total_tokens":2479}}
2025-08-01 18:41:04.307 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 302, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:04.307 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 302 bytes
2025-08-01 18:41:04.705 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"特性——面向对象编程。我们创建一个简单的 `Dog` 类，然后创建它的一个实例（对象）。\n\n这个","role":"assistant"},"index":0}],"created":1754044863,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":756,"prompt_tokens":15,"total_tokens":2503}}
2025-08-01 18:41:04.705 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"特性——面向对象编程。我们创建一个简单的 `Dog` 类，然后创建它的一个实例（对象）。\n\n这个","role":"assistant"},"index":0}],"created":1754044863,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":756,"prompt_tokens":15,"total_tokens":2503}}
2025-08-01 18:41:04.705 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 300, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:04.705 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 300 bytes
2025-08-01 18:41:04.855 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"例子需要两个文件。\n\n**文件 1: `Dog.java`**\n```java\n// 定义一个 Dog","role":"assistant"},"index":0}],"created":1754044863,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":781,"prompt_tokens":15,"total_tokens":2528}}
2025-08-01 18:41:04.855 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"例子需要两个文件。\n\n**文件 1: `Dog.java`**\n```java\n// 定义一个 Dog","role":"assistant"},"index":0}],"created":1754044863,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":781,"prompt_tokens":15,"total_tokens":2528}}
2025-08-01 18:41:04.855 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 305, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:04.855 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 305 bytes
2025-08-01 18:41:05.132 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" 类，这是一个对象的“蓝图”\npublic class Dog {\n    // 成员变量 (属性)\n    String","role":"assistant"},"index":0}],"created":1754044863,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":806,"prompt_tokens":15,"total_tokens":2553}}
2025-08-01 18:41:05.132 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" 类，这是一个对象的“蓝图”\npublic class Dog {\n    // 成员变量 (属性)\n    String","role":"assistant"},"index":0}],"created":1754044863,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":806,"prompt_tokens":15,"total_tokens":2553}}
2025-08-01 18:41:05.132 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 314, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:05.132 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 314 bytes
2025-08-01 18:41:05.421 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" name;\n\n    // 构造方法：用于创建 Dog 对象时初始化 name\n    public Dog(String dogName) {\n","role":"assistant"},"index":0}],"created":1754044864,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":832,"prompt_tokens":15,"total_tokens":2579}}
2025-08-01 18:41:05.421 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" name;\n\n    // 构造方法：用于创建 Dog 对象时初始化 name\n    public Dog(String dogName) {\n","role":"assistant"},"index":0}],"created":1754044864,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":832,"prompt_tokens":15,"total_tokens":2579}}
2025-08-01 18:41:05.422 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 328, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:05.422 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 328 bytes
2025-08-01 18:41:05.717 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"        this.name = dogName;\n        System.out.println(\"一只叫做 \" + name + \" 的","role":"assistant"},"index":0}],"created":1754044864,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":855,"prompt_tokens":15,"total_tokens":2602}}
2025-08-01 18:41:05.717 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"        this.name = dogName;\n        System.out.println(\"一只叫做 \" + name + \" 的","role":"assistant"},"index":0}],"created":1754044864,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":855,"prompt_tokens":15,"total_tokens":2602}}
2025-08-01 18:41:05.717 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 330, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:05.717 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 330 bytes
2025-08-01 18:41:05.918 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"小狗被创建了。\");\n    }\n\n    // 成员方法 (行为)\n    public void bark() {","role":"assistant"},"index":0}],"created":1754044864,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":881,"prompt_tokens":15,"total_tokens":2628}}
2025-08-01 18:41:05.918 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"小狗被创建了。\");\n    }\n\n    // 成员方法 (行为)\n    public void bark() {","role":"assistant"},"index":0}],"created":1754044864,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":881,"prompt_tokens":15,"total_tokens":2628}}
2025-08-01 18:41:05.918 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 314, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:05.918 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 314 bytes
2025-08-01 18:41:06.193 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n        System.out.println(name + \" 正在汪汪叫: 汪！汪！\");\n    }","role":"assistant"},"index":0}],"created":1754044864,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":905,"prompt_tokens":15,"total_tokens":2652}}
2025-08-01 18:41:06.193 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n        System.out.println(name + \" 正在汪汪叫: 汪！汪！\");\n    }","role":"assistant"},"index":0}],"created":1754044864,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":905,"prompt_tokens":15,"total_tokens":2652}}
2025-08-01 18:41:06.193 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 311, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:06.193 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 311 bytes
2025-08-01 18:41:06.411 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n}\n```\n\n**文件 2: `Main.java` (主程序)**\n```java\npublic class Main {","role":"assistant"},"index":0}],"created":1754044865,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":931,"prompt_tokens":15,"total_tokens":2678}}
2025-08-01 18:41:06.411 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n}\n```\n\n**文件 2: `Main.java` (主程序)**\n```java\npublic class Main {","role":"assistant"},"index":0}],"created":1754044865,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":931,"prompt_tokens":15,"total_tokens":2678}}
2025-08-01 18:41:06.411 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 319, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:06.411 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 319 bytes
2025-08-01 18:41:06.671 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n    public static void main(String[] args) {\n        // 使用 \"new\" 关键字和 Dog 的","role":"assistant"},"index":0}],"created":1754044865,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":953,"prompt_tokens":15,"total_tokens":2700}}
2025-08-01 18:41:06.671 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n    public static void main(String[] args) {\n        // 使用 \"new\" 关键字和 Dog 的","role":"assistant"},"index":0}],"created":1754044865,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":953,"prompt_tokens":15,"total_tokens":2700}}
2025-08-01 18:41:06.671 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 330, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:06.671 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 330 bytes
2025-08-01 18:41:06.920 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"构造方法创建一个 Dog 对象\n        // 我们给它取名叫“旺财”\n        Dog myDog = new Dog(\"","role":"assistant"},"index":0}],"created":1754044865,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":979,"prompt_tokens":15,"total_tokens":2726}}
2025-08-01 18:41:06.920 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"构造方法创建一个 Dog 对象\n        // 我们给它取名叫“旺财”\n        Dog myDog = new Dog(\"","role":"assistant"},"index":0}],"created":1754044865,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":979,"prompt_tokens":15,"total_tokens":2726}}
2025-08-01 18:41:06.920 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 321, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:06.920 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 321 bytes
2025-08-01 18:41:07.077 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"旺财\");\n\n        // 调用 myDog 对象的 bark() 方法\n        myDog.bark();\n\n        //","role":"assistant"},"index":0}],"created":1754044865,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1003,"prompt_tokens":15,"total_tokens":2750}}
2025-08-01 18:41:07.077 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"旺财\");\n\n        // 调用 myDog 对象的 bark() 方法\n        myDog.bark();\n\n        //","role":"assistant"},"index":0}],"created":1754044865,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1003,"prompt_tokens":15,"total_tokens":2750}}
2025-08-01 18:41:07.077 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 331, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:07.077 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 331 bytes
2025-08-01 18:41:07.299 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" 创建另一个 Dog 对象\n        Dog anotherDog = new Dog(\"小黑\");\n        anotherDog.bark();\n","role":"assistant"},"index":0}],"created":1754044866,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1026,"prompt_tokens":15,"total_tokens":2773}}
2025-08-01 18:41:07.299 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" 创建另一个 Dog 对象\n        Dog anotherDog = new Dog(\"小黑\");\n        anotherDog.bark();\n","role":"assistant"},"index":0}],"created":1754044866,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1026,"prompt_tokens":15,"total_tokens":2773}}
2025-08-01 18:41:07.299 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 337, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:07.299 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 337 bytes
2025-08-01 18:41:07.500 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"    }\n}\n```\n\n**如何运行:**\n1.  将两个文件 `Dog.java` 和 `Main.","role":"assistant"},"index":0}],"created":1754044866,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1052,"prompt_tokens":15,"total_tokens":2799}}
2025-08-01 18:41:07.500 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"    }\n}\n```\n\n**如何运行:**\n1.  将两个文件 `Dog.java` 和 `Main.","role":"assistant"},"index":0}],"created":1754044866,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1052,"prompt_tokens":15,"total_tokens":2799}}
2025-08-01 18:41:07.500 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 308, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:07.500 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 308 bytes
2025-08-01 18:41:07.720 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"java`保存在同一个文件夹中。\n2.  编译所有 `.java` 文件：`javac *.java` (或者","role":"assistant"},"index":0}],"created":1754044866,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1078,"prompt_tokens":15,"total_tokens":2825}}
2025-08-01 18:41:07.720 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"java`保存在同一个文件夹中。\n2.  编译所有 `.java` 文件：`javac *.java` (或者","role":"assistant"},"index":0}],"created":1754044866,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1078,"prompt_tokens":15,"total_tokens":2825}}
2025-08-01 18:41:07.720 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 307, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:07.720 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 307 bytes
2025-08-01 18:41:07.974 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" `javac Main.java Dog.java`)\n3.  运行包含 `main` 方法的那个类：`java Main`","role":"assistant"},"index":0}],"created":1754044866,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1105,"prompt_tokens":15,"total_tokens":2852}}
2025-08-01 18:41:07.974 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" `javac Main.java Dog.java`)\n3.  运行包含 `main` 方法的那个类：`java Main`","role":"assistant"},"index":0}],"created":1754044866,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1105,"prompt_tokens":15,"total_tokens":2852}}
2025-08-01 18:41:07.974 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 315, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:07.974 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 315 bytes
2025-08-01 18:41:08.226 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n\n**预期输出:**\n```\n一只叫做 旺财 的小狗被创建了。\n旺财 正在汪","role":"assistant"},"index":0}],"created":1754044867,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1130,"prompt_tokens":15,"total_tokens":2877}}
2025-08-01 18:41:08.226 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n\n**预期输出:**\n```\n一只叫做 旺财 的小狗被创建了。\n旺财 正在汪","role":"assistant"},"index":0}],"created":1754044867,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1130,"prompt_tokens":15,"total_tokens":2877}}
2025-08-01 18:41:08.226 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 295, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:08.226 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 295 bytes
2025-08-01 18:41:08.480 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"汪叫: 汪！汪！\n一只叫做 小黑 的小狗被创建了。\n小黑 正在汪","role":"assistant"},"index":0}],"created":1754044867,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1156,"prompt_tokens":15,"total_tokens":2903}}
2025-08-01 18:41:08.480 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"汪叫: 汪！汪！\n一只叫做 小黑 的小狗被创建了。\n小黑 正在汪","role":"assistant"},"index":0}],"created":1754044867,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1156,"prompt_tokens":15,"total_tokens":2903}}
2025-08-01 18:41:08.480 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 285, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:08.480 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 285 bytes
2025-08-01 18:41:08.750 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"汪叫: 汪！汪！\n```\n\n**建议：**\n如果你是初学者，从 **示例","role":"assistant"},"index":0}],"created":1754044867,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1180,"prompt_tokens":15,"total_tokens":2927}}
2025-08-01 18:41:08.750 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"汪叫: 汪！汪！\n```\n\n**建议：**\n如果你是初学者，从 **示例","role":"assistant"},"index":0}],"created":1754044867,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1180,"prompt_tokens":15,"total_tokens":2927}}
2025-08-01 18:41:08.750 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 291, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:08.750 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 291 bytes
2025-08-01 18:41:09.073 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" 1** 开始，确保你能成功运行它。然后尝试 **示例 2**，最后再理解 **示例 3","role":"assistant"},"index":0}],"created":1754044867,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1205,"prompt_tokens":15,"total_tokens":2952}}
2025-08-01 18:41:09.073 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" 1** 开始，确保你能成功运行它。然后尝试 **示例 2**，最后再理解 **示例 3","role":"assistant"},"index":0}],"created":1754044867,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1205,"prompt_tokens":15,"total_tokens":2952}}
2025-08-01 18:41:09.073 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 295, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:09.073 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 295 bytes
2025-08-01 18:41:09.314 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"** 的面向对象概念。\n\n希望这些例子能帮助你入门！如果你有任何问题，或者想了解某个特定的","role":"assistant"},"index":0}],"created":1754044868,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1230,"prompt_tokens":15,"total_tokens":2977}}
2025-08-01 18:41:09.314 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"** 的面向对象概念。\n\n希望这些例子能帮助你入门！如果你有任何问题，或者想了解某个特定的","role":"assistant"},"index":0}],"created":1754044868,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1230,"prompt_tokens":15,"total_tokens":2977}}
2025-08-01 18:41:09.314 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 298, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:09.314 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 298 bytes
2025-08-01 18:41:09.716 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"功能，随时可以再问我。","role":"assistant"},"finish_reason":"stop","index":0}],"created":1754044868,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1238,"prompt_tokens":15,"total_tokens":2985}}
2025-08-01 18:41:09.716 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"功能，随时可以再问我。","role":"assistant"},"finish_reason":"stop","index":0}],"created":1754044868,"id":"uJmMaPHsBaq9qtsPxKuOwAI","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":1238,"prompt_tokens":15,"total_tokens":2985}}
2025-08-01 18:41:09.716 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 285, 响应ID: chatcmpl-894c5272
2025-08-01 18:41:09.717 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 285 bytes
2025-08-01 18:41:09.724 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: [DONE]
2025-08-01 18:41:09.724 [31mWARN [0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : 过滤掉非数据行: [DONE]
2025-08-01 18:41:09.725 [34mINFO [0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google流式请求完成
2025-08-01 18:41:09.725 [34mINFO [0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 流式响应正常完成 - 响应ID: chatcmpl-894c5272, 模型: gemini-2.5-pro
2025-08-01 18:41:09.726 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送SSE事件 - 响应ID: chatcmpl-894c5272, 数据大小: 281 bytes
2025-08-01 18:41:09.727 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送SSE完成事件: [DONE]
2025-08-01 18:41:09.727 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] o.s.w.c.r.async.WebAsyncManager : Async result set, dispatch to /v1/chat/completions
2025-08-01 18:41:09.727 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : SseEmitter安全完成 - 响应ID: chatcmpl-894c5272
2025-08-01 18:41:09.727 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing POST /v1/chat/completions
2025-08-01 18:41:09.727 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-01 18:41:09.727 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-08-01 18:41:09.728 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured POST /v1/chat/completions
2025-08-01 18:41:09.728 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : "ASYNC" dispatch for POST "/v1/chat/completions", parameters={}
2025-08-01 18:41:09.728 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-08-01 18:41:09.728 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-08-01 18:41:09.728 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Exiting from "ASYNC" dispatch, status 200
2025-08-01 18:41:09.729 [39mDEBUG[0;39m 11336 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-01 18:41:09.729 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.LoadBalancerServiceImpl : 结束使用API密钥 - ID: 4, 成功: true
2025-08-01 18:41:09.729 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-01 18:41:09.729 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@503d349]
2025-08-01 18:41:09.729 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1844303337 wrapping com.mysql.cj.jdbc.ConnectionImpl@d5f5e01] will be managed by Spring
2025-08-01 18:41:09.729 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.m.p.A.decrementRequests : ==>  Preparing: UPDATE api_key_load_balance SET current_requests = GREATEST(current_requests - 1, 0), updated_at = NOW() WHERE api_key_id = ?
2025-08-01 18:41:09.730 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.m.p.A.decrementRequests : ==> Parameters: 4(Long)
2025-08-01 18:41:09.731 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.m.p.A.decrementRequests : <==    Updates: 0
2025-08-01 18:41:09.732 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@503d349]
2025-08-01 18:41:09.732 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@503d349] from current transaction
2025-08-01 18:41:09.732 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.m.p.U.updateUsageStats : ==>  Preparing: UPDATE user_api_keys SET usage_count = usage_count + 1, last_used_at = NOW() WHERE id = ?
2025-08-01 18:41:09.732 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.m.p.U.updateUsageStats : ==> Parameters: 4(Long)
2025-08-01 18:41:09.734 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] c.e.p.m.p.U.updateUsageStats : <==    Updates: 1
2025-08-01 18:41:09.734 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@503d349]
2025-08-01 18:41:09.734 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@503d349]
2025-08-01 18:41:09.734 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@503d349]
2025-08-01 18:41:09.734 [39mDEBUG[0;39m 11336 --- [reactor-http-nio-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@503d349]
2025-08-01 18:41:09.750 [34mINFO [0;39m 11336 --- [reactor-http-nio-3] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 流式聊天完成 - 响应ID: chatcmpl-894c5272, API密钥ID: 4
