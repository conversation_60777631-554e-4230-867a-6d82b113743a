<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 用户AI配置Mapper配置 -->
<mapper namespace="com.example.pure.mapper.primary.UserAiConfigMapper">

    <!-- 用户AI配置分组结果映射 -->
    <resultMap id="UserAiConfigResultMap" type="com.example.pure.model.entity.UserAiConfigGroups">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="groupName" column="group_name"/>
        <result property="provider" column="provider"/>
        <result property="customBaseUrl" column="custom_base_url"/>
        <result property="testModel" column="test_model"/>
        <result property="preferredModel" column="preferred_model"/>
        <result property="defaultTemperature" column="default_temperature"/>
        <result property="defaultMaxTokens" column="default_max_tokens"/>
        <result property="defaultTopP" column="default_top_p"/>
        <result property="streamEnabled" column="stream_enabled"/>
        <result property="timeoutSeconds" column="timeout_seconds"/>
        <result property="systemPrompt" column="system_prompt"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, user_id, group_name, provider, custom_base_url, test_model,
        preferred_model, default_temperature, default_max_tokens,
        default_top_p, stream_enabled, timeout_seconds, system_prompt,
        created_at, updated_at
    </sql>

    <!-- 根据用户ID查询AI配置分组列表 -->
    <select id="selectByUserId" resultMap="UserAiConfigResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_ai_config_groups
        WHERE user_id = #{userId}
        ORDER BY created_at ASC
    </select>

    <!-- 根据用户ID和分组名查询AI配置 -->
    <select id="selectByUserIdAndGroupName" resultMap="UserAiConfigResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_ai_config_groups
        WHERE user_id = #{userId} AND group_name = #{groupName}
    </select>

    <!-- 插入用户AI配置分组 -->
    <insert id="insert" parameterType="com.example.pure.model.entity.UserAiConfigGroups"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO user_ai_config_groups (
            user_id, group_name, provider, custom_base_url, test_model,
            preferred_model, default_temperature, default_max_tokens,
            default_top_p, stream_enabled, timeout_seconds, system_prompt
        ) VALUES (
            #{userId}, #{groupName}, #{provider}, #{customBaseUrl}, #{testModel},
            #{preferredModel}, #{defaultTemperature}, #{defaultMaxTokens},
            #{defaultTopP}, #{streamEnabled}, #{timeoutSeconds}, #{systemPrompt}
        )
    </insert>

    <!-- 更新用户AI配置分组 -->
    <update id="updateByUserIdAndGroupName" parameterType="com.example.pure.model.entity.UserAiConfigGroups">
        UPDATE user_ai_config_groups
        SET provider = #{provider},
            custom_base_url = #{customBaseUrl},
            test_model = #{testModel},
            preferred_model = #{preferredModel},
            default_temperature = #{defaultTemperature},
            default_max_tokens = #{defaultMaxTokens},
            default_top_p = #{defaultTopP},
            stream_enabled = #{streamEnabled},
            timeout_seconds = #{timeoutSeconds},
            system_prompt = #{systemPrompt},
            updated_at = NOW()
        WHERE user_id = #{userId} AND group_name = #{groupName}
    </update>

    <!-- 根据用户ID和分组名删除AI配置 -->
    <delete id="deleteByUserIdAndGroupName">
        DELETE FROM user_ai_config_groups
        WHERE user_id = #{userId} AND group_name = #{groupName}
    </delete>

    <!-- 根据用户ID删除所有AI配置分组 -->
    <delete id="deleteByUserId">
        DELETE FROM user_ai_config_groups
        WHERE user_id = #{userId}
    </delete>

    <!-- 检查用户指定分组是否已存在 -->
    <select id="existsByUserIdAndGroupName" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM user_ai_config_groups
        WHERE user_id = #{userId} AND group_name = #{groupName}
    </select>

    <!-- 检查用户是否已有AI配置分组 -->
    <select id="existsByUserId" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM user_ai_config_groups
        WHERE user_id = #{userId}
    </select>

    <!-- ======================== -->
    <!-- DTO查询方法（性能优化）    -->
    <!-- ======================== -->

    <!-- DTO结果映射（只包含前端需要的字段） -->
    <resultMap id="UserConfigGroupDtoResultMap" type="com.example.pure.model.dto.response.openai.UserConfigGroupDto">
        <result property="groupName" column="group_name"/>
        <result property="provider" column="provider"/>
        <result property="customBaseUrl" column="custom_base_url"/>
        <result property="testModel" column="test_model"/>
        <result property="preferredModel" column="preferred_model"/>
        <result property="defaultTemperature" column="default_temperature"/>
        <result property="defaultMaxTokens" column="default_max_tokens"/>
        <result property="defaultTopP" column="default_top_p"/>
        <result property="streamEnabled" column="stream_enabled"/>
        <result property="timeoutSeconds" column="timeout_seconds"/>
        <result property="systemPrompt" column="system_prompt"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <!-- DTO列 -->
    <sql id="Dto_Column_List">
        group_name, provider, custom_base_url, test_model,
        preferred_model, default_temperature, default_max_tokens,
        default_top_p, stream_enabled, timeout_seconds, system_prompt,
        created_at, updated_at
    </sql>

    <!-- 直接查询用户配置分组DTO列表 -->
    <select id="selectConfigGroupDtosByUserId" resultMap="UserConfigGroupDtoResultMap">
        SELECT <include refid="Dto_Column_List"/>
        FROM user_ai_config_groups
        WHERE user_id = #{userId}
        ORDER BY created_at ASC
    </select>

    <!-- 直接查询指定分组的配置DTO -->
    <select id="selectConfigGroupDtoByUserIdAndGroupName" resultMap="UserConfigGroupDtoResultMap">
        SELECT <include refid="Dto_Column_List"/>
        FROM user_ai_config_groups
        WHERE user_id = #{userId} AND group_name = #{groupName}
    </select>
</mapper>
