<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.pure.mapper.primary.OpenaiRequestLogMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.pure.model.entity.RequestLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="request_id" property="requestId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="status_code" property="statusCode" jdbcType="INTEGER"/>
        <result column="request_type" property="requestType" jdbcType="VARCHAR"/>
        <result column="duration_ms" property="durationMs" jdbcType="BIGINT"/>
        <result column="retry_count" property="retryCount" jdbcType="INTEGER"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="provider" property="provider" jdbcType="VARCHAR"/>
        <result column="masked_api_key" property="maskedApiKey" jdbcType="VARCHAR"/>
        <result column="error_message" property="errorMessage" jdbcType="TEXT"/>
        <result column="actual_base_url" property="actualBaseUrl" jdbcType="VARCHAR"/>
        <result column="path_suffix" property="pathSuffix" jdbcType="VARCHAR"/>
        <result column="model_name" property="modelName" jdbcType="VARCHAR"/>
        <result column="extra_info" property="extraInfo" jdbcType="TEXT"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="is_deleted" property="isDeleted" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, request_id, user_id, start_time, end_time, status, status_code, request_type,
        duration_ms, retry_count, group_name, provider, masked_api_key, error_message,
        actual_base_url, path_suffix, model_name, extra_info, created_at, updated_at, is_deleted
    </sql>

    <!-- 插入请求日志 -->
    <insert id="insert" parameterType="com.example.pure.model.entity.RequestLog" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO request_logs (
            request_id, user_id, start_time, end_time, status, status_code, request_type,
            duration_ms, retry_count, group_name, provider, masked_api_key, error_message,
            actual_base_url, path_suffix, model_name, extra_info, created_at, updated_at, is_deleted
        ) VALUES (
            #{requestId}, #{userId}, #{startTime}, #{endTime}, #{status}, #{statusCode}, #{requestType},
            #{durationMs}, #{retryCount}, #{groupName}, #{provider}, #{maskedApiKey}, #{errorMessage},
            #{actualBaseUrl}, #{pathSuffix}, #{modelName}, #{extraInfo}, NOW(), NOW(), #{isDeleted}
        )
    </insert>

    <!-- 分页查询请求日志 -->
    <select id="selectRequestLogsPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM request_logs
        WHERE is_deleted = 0
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="provider != null and provider != ''">
            AND provider = #{provider}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="requestType != null and requestType != ''">
            AND request_type = #{requestType}
        </if>
        <if test="modelName != null and modelName != ''">
            AND model_name LIKE CONCAT('%', #{modelName}, '%')
        </if>
        <if test="startTime != null">
            AND start_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND end_time &lt;= #{endTime}
        </if>
        ORDER BY created_at DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计请求日志总数 -->
    <select id="countRequestLogs" resultType="long">
        SELECT COUNT(*)
        FROM request_logs
        WHERE is_deleted = 0
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="provider != null and provider != ''">
            AND provider = #{provider}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="requestType != null and requestType != ''">
            AND request_type = #{requestType}
        </if>
        <if test="modelName != null and modelName != ''">
            AND model_name LIKE CONCAT('%', #{modelName}, '%')
        </if>
        <if test="startTime != null">
            AND start_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND end_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 根据用户ID查询请求日志 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM request_logs
        WHERE user_id = #{userId} AND is_deleted = 0
        ORDER BY created_at DESC
    </select>

    <!-- 根据请求ID查询请求日志 -->
    <select id="selectByRequestId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM request_logs
        WHERE request_id = #{requestId} AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 查询指定时间范围内的请求日志统计 -->
    <select id="selectStatistics" resultMap="BaseResultMap">
        SELECT
        provider,
        status,
        COUNT(*) as total_count,
        AVG(duration_ms) as avg_duration,
        MAX(duration_ms) as max_duration,
        MIN(duration_ms) as min_duration
        FROM request_logs
        WHERE is_deleted = 0
        <if test="startTime != null">
            AND start_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND end_time &lt;= #{endTime}
        </if>
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        GROUP BY provider, status
        ORDER BY provider, status
    </select>

    <!-- 删除指定时间之前的日志 -->
    <delete id="deleteBeforeTime">
        DELETE FROM request_logs
        WHERE created_at &lt; #{beforeTime}
    </delete>

    <!-- 查询失败的请求日志 -->
    <select id="selectFailedRequestsPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM request_logs
        WHERE is_deleted = 0 AND status = '失败'
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="startTime != null">
            AND start_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND end_time &lt;= #{endTime}
        </if>
        ORDER BY created_at DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计失败请求日志总数 -->
    <select id="countFailedRequests" resultType="long">
        SELECT COUNT(*)
        FROM request_logs
        WHERE is_deleted = 0 AND status = '失败'
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="startTime != null">
            AND start_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND end_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 查询耗时较长的请求日志 -->
    <select id="selectSlowRequestsPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM request_logs
        WHERE is_deleted = 0 AND duration_ms >= #{minDuration}
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        ORDER BY duration_ms DESC, created_at DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计慢请求日志总数 -->
    <select id="countSlowRequests" resultType="long">
        SELECT COUNT(*)
        FROM request_logs
        WHERE is_deleted = 0 AND duration_ms >= #{minDuration}
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
    </select>

</mapper>
