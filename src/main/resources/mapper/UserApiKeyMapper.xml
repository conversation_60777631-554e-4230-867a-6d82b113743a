<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 用户API密钥Mapper配置 -->
<mapper namespace="com.example.pure.mapper.primary.UserApiKeyMapper">

    <!-- 用户API密钥结果映射 -->
    <resultMap id="UserApiKeyResultMap" type="com.example.pure.model.entity.UserApiKey">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="configGroupId" column="config_group_id"/>
        <result property="apiKeyEncrypted" column="api_key_encrypted"/>
        <result property="isActive" column="is_active"/>
        <result property="priority" column="priority"/>
        <result property="usageCount" column="usage_count"/>
        <result property="lastUsedAt" column="last_used_at"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="provider" column="provider" typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        k.id, k.user_id, k.config_group_id, k.api_key_encrypted,
        k.is_active, k.priority, k.usage_count, k.last_used_at,
        k.created_at, k.updated_at, g.provider
    </sql>

    <!-- 根据ID查询API密钥 -->
    <select id="selectById" resultMap="UserApiKeyResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_api_keys k
        LEFT JOIN user_ai_config_groups g ON k.config_group_id = g.id
        WHERE k.id = #{id}
    </select>

    <!-- 根据用户ID查询所有API密钥 -->
    <select id="selectByUserId" resultMap="UserApiKeyResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_api_keys k
        LEFT JOIN user_ai_config_groups g ON k.config_group_id = g.id
        WHERE k.user_id = #{userId}
        ORDER BY k.priority ASC, k.created_at ASC
    </select>

    <!-- 根据用户ID和配置分组ID查询API密钥 -->
    <select id="selectByUserIdAndConfigGroupId" resultMap="UserApiKeyResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_api_keys k
        LEFT JOIN user_ai_config_groups g ON k.config_group_id = g.id
        WHERE k.user_id = #{userId} AND k.config_group_id = #{configGroupId}
        ORDER BY k.priority ASC, k.created_at ASC
    </select>

    <!-- 查询用户指定配置分组的活跃API密钥 -->
    <select id="selectActiveByUserIdAndConfigGroupId" resultMap="UserApiKeyResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_api_keys k
        LEFT JOIN user_ai_config_groups g ON k.config_group_id = g.id
        WHERE k.user_id = #{userId} AND k.config_group_id = #{configGroupId}
        AND k.is_active = TRUE
        ORDER BY k.priority ASC, k.created_at ASC
    </select>

    <!-- 根据用户ID和提供商查询活跃API密钥（兼容方法） -->
    <select id="selectActiveByUserIdAndProvider" resultMap="UserApiKeyResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_api_keys k
        LEFT JOIN user_ai_config_groups g ON k.config_group_id = g.id
        WHERE k.user_id = #{userId} AND g.provider = #{provider}
        AND k.is_active = TRUE
        ORDER BY k.priority ASC, k.created_at ASC
    </select>

    <!-- 插入API密钥 -->
    <insert id="insert" parameterType="com.example.pure.model.entity.UserApiKey"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO user_api_keys (
            user_id, config_group_id, api_key_encrypted,
            is_active, priority
        ) VALUES (
            #{userId}, #{configGroupId}, #{apiKeyEncrypted},
            #{isActive}, #{priority}
        )
    </insert>

    <!-- 更新API密钥 -->
    <update id="updateById" parameterType="com.example.pure.model.entity.UserApiKey">
        UPDATE user_api_keys
        SET api_key_encrypted = #{apiKeyEncrypted},
            is_active = #{isActive},
            priority = #{priority},
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新API密钥使用统计 -->
    <update id="updateUsageStats">
        UPDATE user_api_keys
        SET usage_count = usage_count + 1,
            last_used_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 删除API密钥 -->
    <delete id="deleteById">
        DELETE FROM user_api_keys
        WHERE id = #{id}
    </delete>

    <!-- 根据用户ID删除所有API密钥 -->
    <delete id="deleteByUserId">
        DELETE FROM user_api_keys
        WHERE user_id = #{userId}
    </delete>

    <!-- 根据用户ID和配置分组ID删除API密钥 -->
    <delete id="deleteByUserIdAndConfigGroupId">
        DELETE FROM user_api_keys
        WHERE user_id = #{userId} AND config_group_id = #{configGroupId}
    </delete>

    <!-- 批量插入API密钥 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO user_api_keys (
            user_id, config_group_id, api_key_encrypted,
            is_active, priority
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.configGroupId}, #{item.apiKeyEncrypted},
             #{item.isActive}, #{item.priority})
        </foreach>
    </insert>
</mapper>
