-- 用户表（核心认证信息）
CREATE TABLE IF NOT EXISTS user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(100) NOT NULL,
    account_non_expired BOOLEAN DEFAULT TRUE,
    account_non_locked BOOLEAN DEFAULT TRUE,
    credentials_non_expired BOOLEAN DEFAULT TRUE,
    enabled BOOLEAN DEFAULT TRUE,
    last_login_time DATETIME(3),
    created_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    updated_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    ban_expires_at DATETIME(3),
    refresh_token TEXT,
    refresh_token_expires DATETIME(3),
    INDEX idx_username (username),
    INDEX idx_last_login (last_login_time)
);

-- 用户资料表（扩展信息）
CREATE TABLE IF NOT EXISTS user_profile (
    id BIGINT PRIMARY KEY,
    username VARCHAR(50),
    phone VARCHAR(20),
    email VARCHAR(100) UNIQUE,
    nickname VARCHAR(50),
    avatar VARCHAR(255),
    description TEXT,
    INDEX idx_email (email),
    INDEX idx_nickname (nickname)
);

-- 角色表
CREATE TABLE IF NOT EXISTS role (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE
);

-- 用户角色关联表（无外键约束）
CREATE TABLE IF NOT EXISTS user_role (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    PRIMARY KEY (user_id, role_id)
);

-- 访问日志表
CREATE TABLE IF NOT EXISTS access_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    access_type VARCHAR(50),  -- 'LOGIN' 或 'GET_USER_INFO'
    access_count INT DEFAULT 1,
    access_date DATE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_type_date (user_id, access_type, access_date),
    INDEX idx_user_id (user_id)
);

-- ========================
-- AI大模型API转发系统表结构
-- ========================

-- 用户AI配置分组表
CREATE TABLE IF NOT EXISTS user_ai_config_groups (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    group_name VARCHAR(100) NOT NULL,
    provider VARCHAR(50) NOT NULL,
    custom_base_url VARCHAR(500),
    test_model VARCHAR(100),
    preferred_model VARCHAR(50) DEFAULT 'gpt-4.0-turbo',
    default_temperature DECIMAL(3,2) DEFAULT 0.7,
    default_max_tokens INT DEFAULT 4096,
    default_top_p DECIMAL(3,2) DEFAULT 1.0,
    stream_enabled BOOLEAN DEFAULT TRUE,
    timeout_seconds INT DEFAULT 30,
    system_prompt TEXT,
    created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
    updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    UNIQUE KEY uk_user_group (user_id, group_name),
    INDEX idx_user_id (user_id),
    INDEX idx_provider (provider)
);

-- 用户API密钥表
CREATE TABLE IF NOT EXISTS user_api_keys (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    config_group_id BIGINT NOT NULL,
    api_key_encrypted TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    priority INT DEFAULT 1,
    usage_count BIGINT DEFAULT 0,
    last_used_at DATETIME(3) NULL,
    created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
    updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    INDEX idx_user_group (user_id, config_group_id),
    INDEX idx_active_priority (is_active, priority)
);

-- 负载均衡状态表
CREATE TABLE IF NOT EXISTS api_key_load_balance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    config_group_id BIGINT NOT NULL,
    api_key_id BIGINT NOT NULL,
    current_requests INT DEFAULT 0,
    total_requests BIGINT DEFAULT 0,
    error_count INT DEFAULT 0,
    last_error_at DATETIME(3) NULL,
    is_healthy BOOLEAN DEFAULT TRUE,
    updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    INDEX idx_user_group (user_id, config_group_id),
    INDEX idx_healthy (is_healthy),
    INDEX idx_api_key_id (api_key_id)
);

-- 聊天会话表
CREATE TABLE IF NOT EXISTS chat_sessions (
    session_id VARCHAR(64) PRIMARY KEY,
    user_id BIGINT NOT NULL,
    config_group_id BIGINT,
    model VARCHAR(50),
    api_key_id BIGINT,
    temperature DECIMAL(3,2),
    max_tokens INT,
    custom_prompt TEXT,
    message_count INT DEFAULT 0,
    expires_at DATETIME(3),
    created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
    INDEX idx_user_id (user_id),
    INDEX idx_expires (expires_at),
    INDEX idx_api_key_id (api_key_id),
    INDEX idx_user_group (user_id, config_group_id)
);

-- 兼容API密钥表
CREATE TABLE IF NOT EXISTS compatible_api_keys (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    key_name VARCHAR(100) NOT NULL,
    key_hash VARCHAR(64) NOT NULL UNIQUE,
    salt VARCHAR(64) NOT NULL,
    usage_count BIGINT DEFAULT 0,
    last_used_at DATETIME(3) NULL,
    created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
    updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    INDEX idx_user_id (user_id),
    INDEX idx_key_hash (key_hash),
    INDEX idx_created_at (created_at)
);

-- 初始化基础角色
INSERT INTO role (name) VALUES ('ROLE_USER'), ('ROLE_ADMIN') ON DUPLICATE KEY UPDATE name=name;
