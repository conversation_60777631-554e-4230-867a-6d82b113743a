package com.example.pure.model.dto.response.openai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.Instant;

/**
 * 用户AI配置分组DTO
 * <p>
 * 用于用户AI配置分组的传输和展示
 * </p>
 */
@Data
@Schema(description = "用户AI配置分组")
public class UserConfigGroupDto {

    // 注意：不暴露ID，避免前端直接操作数据库主键

    /**
     * 分组名称
     */
    @NotBlank(message = "分组名称不能为空")
    @Size(max = 100, message = "分组名称长度不能超过100")
    @Schema(description = "分组名称", example = "默认分组", required = true)
    private String groupName;

    /**
     * AI提供商类型
     */
    @NotBlank(message = "提供商类型不能为空")
    @Size(max = 50, message = "提供商类型长度不能超过50")
    @Schema(description = "提供商类型", example = "OPENAI", required = true)
    private String provider;

    /**
     * 自定义基础URL
     */
    @Size(max = 500, message = "自定义基础URL长度不能超过500")
    @Schema(description = "自定义基础URL", example = "https://api.openai.com/v1")
    private String customBaseUrl;

    /**
     * 测试模型
     */
    @Size(max = 100, message = "测试模型名称长度不能超过100")
    @Schema(description = "测试模型", example = "gpt-3.5-turbo")
    private String testModel;

    /**
     * 首选模型
     */
    @Schema(description = "首选模型", example = "gpt-4.0-turbo")
    private String preferredModel;

    /**
     * 默认温度参数
     */
    @DecimalMin(value = "0.0", message = "温度参数不能小于0")
    @DecimalMax(value = "2.0", message = "温度参数不能大于2")
    @Schema(description = "默认温度参数", example = "0.7", minimum = "0", maximum = "2")
    private BigDecimal defaultTemperature;

    /**
     * 默认最大token数
     */
    @Min(value = 1, message = "最大token数不能小于1")
    @Max(value = 32768, message = "最大token数不能大于32768")
    @Schema(description = "默认最大token数", example = "4096")
    private Integer defaultMaxTokens;

    /**
     * 默认top_p参数
     */
    @DecimalMin(value = "0.0", message = "top_p参数不能小于0")
    @DecimalMax(value = "1.0", message = "top_p参数不能大于1")
    @Schema(description = "默认top_p参数", example = "1.0", minimum = "0", maximum = "1")
    private BigDecimal defaultTopP;

    /**
     * 是否启用流式输出
     */
    @Schema(description = "是否启用流式输出", example = "true")
    private Boolean streamEnabled;

    /**
     * 超时时间(秒)
     */
    @Min(value = 1, message = "超时时间不能小于1秒")
    @Max(value = 300, message = "超时时间不能大于300秒")
    @Schema(description = "超时时间(秒)", example = "30")
    private Integer timeoutSeconds;

    /**
     * 系统提示词
     */
    @Schema(description = "系统提示词", example = "你是一个有用的AI助手")
    private String systemPrompt;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-08-01T09:00:00Z")
    private Instant createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "最后更新时间", example = "2025-08-01T10:30:00Z")
    private Instant updatedAt;
}
