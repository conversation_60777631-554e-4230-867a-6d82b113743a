package com.example.pure.model.dto.request.openai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

/**
 * 批量添加API密钥请求DTO
 * <p>
 * 用于批量添加API密钥到指定分组
 * </p>
 */
@Data
@Schema(description = "批量添加API密钥请求")
public class BatchAddApiKeyRequest {

    /**
     * 配置分组ID
     */
    @NotNull(message = "配置分组ID不能为空")
    @Schema(description = "配置分组ID", example = "1", required = true)
    private Long configGroupId;

    /**
     * API密钥列表
     */
    @NotEmpty(message = "API密钥列表不能为空")
    @Size(max = 10, message = "一次最多添加10个API密钥")
    @Schema(description = "API密钥列表", required = true)
    private List<@Valid String> apiKeys;

    /**
     * 优先级
     */
    @Min(value = 1, message = "优先级不能小于1")
    @Max(value = 100, message = "优先级不能大于100")
    @Schema(description = "优先级（数字越小优先级越高）", example = "1")
    private Integer priority = 1;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活", example = "true")
    private Boolean isActive = true;


}
