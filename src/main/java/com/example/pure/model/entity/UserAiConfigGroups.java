package com.example.pure.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 用户AI配置分组实体类
 * <p>
 * 存储用户的AI聊天偏好设置，支持按分组管理不同的配置和提供商
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class UserAiConfigGroups {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * AI提供商类型
     */
    private String provider;

    /**
     * 自定义基础URL（用于请求AI大模型的实际地址）
     */
    private String customBaseUrl;

    /**
     * 测试模型（用于验证API密钥有效性）
     */
    private String testModel;

    /**
     * 首选模型
     */
    private String preferredModel;

    /**
     * 默认温度参数 (0.0-2.0)
     */
    private BigDecimal defaultTemperature;

    /**
     * 默认最大token数
     */
    private Integer defaultMaxTokens;

    /**
     * 默认top_p参数 (0.0-1.0)
     */
    private BigDecimal defaultTopP;

    /**
     * 是否启用流式输出
     */
    private Boolean streamEnabled;

    /**
     * 超时时间(秒)
     */
    private Integer timeoutSeconds;

    /**
     * 系统提示词
     */
    private String systemPrompt;

    /**
     * 创建时间
     */
    private Instant createdAt;

    /**
     * 更新时间
     */
    private Instant updatedAt;
}
