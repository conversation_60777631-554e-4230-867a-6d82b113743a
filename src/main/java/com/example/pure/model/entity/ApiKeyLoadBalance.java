package com.example.pure.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.Instant;

/**
 * API密钥负载均衡状态实体类
 * <p>
 * 记录每个API密钥的负载状态，用于智能负载均衡算法
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ApiKeyLoadBalance {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 配置分组ID
     */
    private Long configGroupId;

    /**
     * API密钥ID
     */
    private Long apiKeyId;

    /**
     * 当前并发请求数
     */
    private Integer currentRequests;

    /**
     * 总请求数统计
     */
    private Long totalRequests;

    /**
     * 错误次数统计
     */
    private Integer errorCount;

    /**
     * 最后错误时间
     */
    private Instant lastErrorAt;

    /**
     * 是否健康
     */
    private Boolean isHealthy;

    /**
     * 更新时间
     */
    private Instant updatedAt;
}
