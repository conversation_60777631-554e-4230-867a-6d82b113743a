package com.example.pure.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 请求日志实体类
 * <p>
 * 用于存储API请求的详细日志信息到数据库
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("request_logs")
public class RequestLog {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 请求ID（用于追踪）
     */
    @TableField("request_id")
    private String requestId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 请求开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 请求结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 请求状态（成功/失败）
     */
    @TableField("status")
    private String status;

    /**
     * HTTP状态码
     */
    @TableField("status_code")
    private Integer statusCode;

    /**
     * 请求类型（stream/non-stream）
     */
    @TableField("request_type")
    private String requestType;

    /**
     * 花费时间（毫秒）
     */
    @TableField("duration_ms")
    private Long durationMs;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 配置分组名称
     */
    @TableField("group_name")
    private String groupName;

    /**
     * AI提供商
     */
    @TableField("provider")
    private String provider;

    /**
     * 使用的API密钥（脱敏）
     */
    @TableField("masked_api_key")
    private String maskedApiKey;

    /**
     * 错误信息（失败时）
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * WebClient请求的实际AI大模型基础路径
     */
    @TableField("actual_base_url")
    private String actualBaseUrl;

    /**
     * 请求路径后缀
     */
    @TableField("path_suffix")
    private String pathSuffix;

    /**
     * 请求的模型名称
     */
    @TableField("model_name")
    private String modelName;

    /**
     * 额外信息（JSON格式）
     */
    @TableField("extra_info")
    private String extraInfo;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean isDeleted;
}
