package com.example.pure.mapper.primary;

import com.example.pure.model.entity.UserApiKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户API密钥数据访问层
 * <p>
 * 提供用户API密钥的CRUD操作
 * </p>
 */
@Mapper
public interface UserApiKeyMapper {

    /**
     * 根据ID查询API密钥
     *
     * @param id 密钥ID
     * @return API密钥信息
     */
    UserApiKey selectById(Long id);

    /**
     * 根据用户ID查询所有API密钥
     *
     * @param userId 用户ID
     * @return API密钥列表
     */
    List<UserApiKey> selectByUserId(Long userId);

    /**
     * 根据用户ID和配置分组ID查询API密钥
     *
     * @param userId   用户ID
     * @param configGroupId 配置分组ID
     * @return API密钥列表
     */
    List<UserApiKey> selectByUserIdAndConfigGroupId(@Param("userId") Long userId, @Param("configGroupId") Long configGroupId);

    /**
     * 查询用户指定配置分组的活跃API密钥
     *
     * @param userId   用户ID
     * @param configGroupId 配置分组ID
     * @return 活跃的API密钥列表
     */
    List<UserApiKey> selectActiveByUserIdAndConfigGroupId(@Param("userId") Long userId, @Param("configGroupId") Long configGroupId);

    /**
     * 根据用户ID和提供商查询活跃API密钥（兼容方法）
     *
     * @param userId   用户ID
     * @param provider 提供商类型
     * @return 活跃的API密钥列表
     */
    List<UserApiKey> selectActiveByUserIdAndProvider(@Param("userId") Long userId, @Param("provider") String provider);

    /**
     * 插入API密钥
     *
     * @param apiKey API密钥信息
     * @return 影响行数
     */
    int insert(UserApiKey apiKey);

    /**
     * 更新API密钥
     *
     * @param apiKey API密钥信息
     * @return 影响行数
     */
    int updateById(UserApiKey apiKey);

    /**
     * 更新API密钥使用统计
     *
     * @param id 密钥ID
     * @return 影响行数
     */
    int updateUsageStats(Long id);

    /**
     * 删除API密钥
     *
     * @param id 密钥ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据用户ID删除所有API密钥
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(Long userId);

    /**
     * 根据用户ID和配置分组ID删除API密钥
     *
     * @param userId 用户ID
     * @param configGroupId 配置分组ID
     * @return 影响行数
     */
    int deleteByUserIdAndConfigGroupId(@Param("userId") Long userId, @Param("configGroupId") Long configGroupId);

    /**
     * 批量插入API密钥
     *
     * @param apiKeys API密钥列表
     * @return 影响行数
     */
    int batchInsert(List<UserApiKey> apiKeys);
}
