package com.example.pure.mapper.primary;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.pure.model.entity.RequestLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 请求日志Mapper接口
 * <p>
 * 提供请求日志的数据库操作方法
 * </p>
 */
@Mapper
public interface RequestLogMapper extends BaseMapper<RequestLog> {

    /**
     * 分页查询请求日志
     *
     * @param page 分页参数
     * @param userId 用户ID（可选）
     * @param provider 提供商（可选）
     * @param status 状态（可选）
     * @param requestType 请求类型（可选）
     * @param modelName 模型名称（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    IPage<RequestLog> selectRequestLogsPage(
            Page<RequestLog> page,
            @Param("userId") Long userId,
            @Param("provider") String provider,
            @Param("status") String status,
            @Param("requestType") String requestType,
            @Param("modelName") String modelName,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 根据用户ID查询请求日志
     *
     * @param userId 用户ID
     * @return 请求日志列表
     */
    List<RequestLog> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据请求ID查询请求日志
     *
     * @param requestId 请求ID
     * @return 请求日志
     */
    RequestLog selectByRequestId(@Param("requestId") String requestId);

    /**
     * 查询指定时间范围内的请求日志统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param userId 用户ID（可选）
     * @return 统计结果
     */
    List<RequestLog> selectStatistics(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("userId") Long userId
    );

    /**
     * 删除指定时间之前的日志（物理删除，用于清理历史数据）
     *
     * @param beforeTime 时间点
     * @return 删除的记录数
     */
    int deleteBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 查询失败的请求日志
     *
     * @param page 分页参数
     * @param userId 用户ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    IPage<RequestLog> selectFailedRequestsPage(
            Page<RequestLog> page,
            @Param("userId") Long userId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 查询耗时较长的请求日志
     *
     * @param page 分页参数
     * @param minDuration 最小耗时（毫秒）
     * @param userId 用户ID（可选）
     * @return 分页结果
     */
    IPage<RequestLog> selectSlowRequestsPage(
            Page<RequestLog> page,
            @Param("minDuration") Long minDuration,
            @Param("userId") Long userId
    );
}
