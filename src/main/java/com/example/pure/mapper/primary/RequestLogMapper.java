package com.example.pure.mapper.primary;

import com.example.pure.model.entity.RequestLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 请求日志Mapper接口
 * <p>
 * 提供请求日志的数据库操作方法
 * </p>
 */
@Mapper
public interface RequestLogMapper {

    /**
     * 插入请求日志
     *
     * @param requestLog 请求日志对象
     * @return 影响的行数
     */
    int insert(RequestLog requestLog);

    /**
     * 分页查询请求日志
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @param userId 用户ID（可选）
     * @param provider 提供商（可选）
     * @param status 状态（可选）
     * @param requestType 请求类型（可选）
     * @param modelName 模型名称（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 日志列表
     */
    List<RequestLog> selectRequestLogsPage(
            @Param("offset") int offset,
            @Param("limit") int limit,
            @Param("userId") Long userId,
            @Param("provider") String provider,
            @Param("status") String status,
            @Param("requestType") String requestType,
            @Param("modelName") String modelName,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 统计请求日志总数
     *
     * @param userId 用户ID（可选）
     * @param provider 提供商（可选）
     * @param status 状态（可选）
     * @param requestType 请求类型（可选）
     * @param modelName 模型名称（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 总数
     */
    long countRequestLogs(
            @Param("userId") Long userId,
            @Param("provider") String provider,
            @Param("status") String status,
            @Param("requestType") String requestType,
            @Param("modelName") String modelName,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 根据用户ID查询请求日志
     *
     * @param userId 用户ID
     * @return 请求日志列表
     */
    List<RequestLog> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据请求ID查询请求日志
     *
     * @param requestId 请求ID
     * @return 请求日志
     */
    RequestLog selectByRequestId(@Param("requestId") String requestId);

    /**
     * 查询指定时间范围内的请求日志统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param userId 用户ID（可选）
     * @return 统计结果
     */
    List<RequestLog> selectStatistics(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("userId") Long userId
    );

    /**
     * 删除指定时间之前的日志（物理删除，用于清理历史数据）
     *
     * @param beforeTime 时间点
     * @return 删除的记录数
     */
    int deleteBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 查询失败的请求日志
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @param userId 用户ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 日志列表
     */
    List<RequestLog> selectFailedRequestsPage(
            @Param("offset") int offset,
            @Param("limit") int limit,
            @Param("userId") Long userId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 统计失败请求日志总数
     *
     * @param userId 用户ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 总数
     */
    long countFailedRequests(
            @Param("userId") Long userId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 查询耗时较长的请求日志
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @param minDuration 最小耗时（毫秒）
     * @param userId 用户ID（可选）
     * @return 日志列表
     */
    List<RequestLog> selectSlowRequestsPage(
            @Param("offset") int offset,
            @Param("limit") int limit,
            @Param("minDuration") Long minDuration,
            @Param("userId") Long userId
    );

    /**
     * 统计慢请求日志总数
     *
     * @param minDuration 最小耗时（毫秒）
     * @param userId 用户ID（可选）
     * @return 总数
     */
    long countSlowRequests(
            @Param("minDuration") Long minDuration,
            @Param("userId") Long userId
    );
}
