package com.example.pure.mapper.primary;

import com.example.pure.model.entity.UserAiConfigGroups;
import com.example.pure.model.dto.response.openai.UserConfigGroupDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户AI配置分组数据访问层
 * <p>
 * 提供用户AI配置分组的CRUD操作
 * </p>
 */
@Mapper
public interface UserAiConfigMapper {

    /**
     * 根据用户ID查询所有AI配置分组
     *
     * @param userId 用户ID
     * @return 用户AI配置分组列表
     */
    List<UserAiConfigGroups> selectByUserId(Long userId);

    /**
     * 根据用户ID和分组名查询AI配置
     *
     * @param userId 用户ID
     * @param groupName 分组名称
     * @return 用户AI配置
     */
    UserAiConfigGroups selectByUserIdAndGroupName(@Param("userId") Long userId, @Param("groupName") String groupName);

    /**
     * 插入用户AI配置分组
     *
     * @param config 用户AI配置
     * @return 影响行数
     */
    int insert(UserAiConfigGroups config);

    /**
     * 更新用户AI配置分组
     *
     * @param config 用户AI配置
     * @return 影响行数
     */
    int updateByUserIdAndGroupName(UserAiConfigGroups config);

    /**
     * 根据用户ID和分组名删除AI配置
     *
     * @param userId 用户ID
     * @param groupName 分组名称
     * @return 影响行数
     */
    int deleteByUserIdAndGroupName(@Param("userId") Long userId, @Param("groupName") String groupName);

    /**
     * 根据用户ID删除所有AI配置分组
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(Long userId);

    /**
     * 检查用户指定分组是否已存在
     *
     * @param userId 用户ID
     * @param groupName 分组名称
     * @return 是否存在
     */
    boolean existsByUserIdAndGroupName(@Param("userId") Long userId, @Param("groupName") String groupName);

    /**
     * 检查用户是否已有AI配置分组
     *
     * @param userId 用户ID
     * @return 是否存在
     */
    boolean existsByUserId(Long userId);

    // ========================
    // DTO查询方法（性能优化）
    // ========================

    /**
     * 直接查询用户配置分组DTO列表（性能优化）
     * <p>
     * 避免Entity→DTO转换，直接返回前端需要的数据格式
     * </p>
     *
     * @param userId 用户ID
     * @return 用户AI配置分组DTO列表
     */
    List<UserConfigGroupDto> selectConfigGroupDtosByUserId(Long userId);

    /**
     * 直接查询指定分组的配置DTO（性能优化）
     *
     * @param userId 用户ID
     * @param groupName 分组名称
     * @return 用户AI配置分组DTO
     */
    UserConfigGroupDto selectConfigGroupDtoByUserIdAndGroupName(@Param("userId") Long userId, @Param("groupName") String groupName);
}
