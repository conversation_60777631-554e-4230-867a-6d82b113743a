package com.example.pure.controller.openai;

import com.example.pure.common.Result;
import com.example.pure.model.dto.request.CreateCompatibleKeyRequest;
import com.example.pure.model.dto.request.openai.BatchAddApiKeyRequest;
import com.example.pure.model.dto.response.CompatibleApiKeyDto;
import com.example.pure.model.dto.response.openai.ApiKeyDto;
import com.example.pure.model.dto.response.openai.BatchAddApiKeyResult;
import com.example.pure.model.dto.response.openai.UserConfigGroupDto;
import com.example.pure.model.entity.UserAiConfigGroups;
import com.example.pure.model.entity.UserApiKey;
import com.example.pure.security.CustomUserDetails;
import com.example.pure.service.openai.AiConfigService;
import com.example.pure.service.openai.CompatibleApiKeyService;
import com.example.pure.service.openai.LoadBalancerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AI配置管理控制器
 * <p>
 * 提供用户AI配置和API密钥的管理功能
 * </p>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/ai/config")
@RequiredArgsConstructor
@Tag(name = "AI配置管理", description = "用户AI配置和API密钥管理接口")
public class AiConfigController {

    private final AiConfigService aiConfigService;
    private final LoadBalancerService loadBalancerService;

    private final CompatibleApiKeyService compatibleApiKeyService;

    // ========================
    // 用户AI配置分组管理
    // ========================

    /**
     * 获取用户所有AI配置分组（性能优化版本）
     */
    @GetMapping("/config-groups")
    @Operation(summary = "获取用户AI配置分组列表", description = "获取当前用户的所有AI配置分组")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<List<UserConfigGroupDto>> getUserConfigGroups(Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            // 🚀 性能优化：直接查询DTO，避免Entity→DTO转换
            List<UserConfigGroupDto> dtos = aiConfigService.getUserConfigGroupDtos(userId);

            log.debug("获取用户AI配置分组列表成功 - 用户ID: {}, 数量: {}", userId, dtos.size());
            return Result.success("获取成功", dtos);
        } catch (Exception e) {
            log.error("获取用户AI配置分组列表失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户指定分组的AI配置（性能优化版本）
     */
    @GetMapping("/config-groups/{groupName}")
    @Operation(summary = "获取指定分组AI配置", description = "获取当前用户指定分组的AI配置")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<UserConfigGroupDto> getUserConfigByGroup(
            @PathVariable String groupName,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            // 🚀 性能优化：直接查询DTO，避免Entity→DTO转换
            UserConfigGroupDto dto = aiConfigService.getUserConfigGroupDto(userId, groupName);
            if (dto == null) {
                return Result.errorTyped(404, "配置分组不存在");
            }

            log.debug("获取用户指定分组AI配置成功 - 用户ID: {}, 分组: {}", userId, groupName);
            return Result.success("获取成功", dto);
        } catch (Exception e) {
            log.error("获取用户指定分组AI配置失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 创建或更新用户AI配置分组
     */
    @PutMapping("/config-groups")
    @Operation(summary = "创建或更新AI配置分组", description = "创建或更新当前用户的AI配置分组")
    @ApiResponse(responseCode = "200", description = "操作成功")
    public Result<UserConfigGroupDto> createOrUpdateUserConfigGroup(
            @Valid @RequestBody UserConfigGroupDto configDto,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            UserAiConfigGroups config = convertToUserAiConfig(configDto);

            UserAiConfigGroups updatedConfig = aiConfigService.createOrUpdateUserConfigGroup(userId, config);
            UserConfigGroupDto dto = convertToUserConfigGroupDto(updatedConfig);

            log.info("创建或更新用户AI配置分组成功 - 用户ID: {}, 分组: {}", userId, configDto.getGroupName());
            return Result.success("操作成功", dto);
        } catch (Exception e) {
            log.error("创建或更新用户AI配置分组失败", e);
            return Result.errorTyped(500, "操作失败: " + e.getMessage());
        }
    }

    /**
     * 删除用户AI配置分组
     */
    @DeleteMapping("/config-groups/{groupName}")
    @Operation(summary = "删除AI配置分组", description = "删除当前用户的指定AI配置分组")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public Result<String> deleteUserConfigGroup(
            @PathVariable String groupName,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            boolean deleted = aiConfigService.deleteUserConfigGroup(userId, groupName);

            if (deleted) {
                log.info("删除用户AI配置分组成功 - 用户ID: {}, 分组: {}", userId, groupName);
                return Result.success("删除成功", "删除成功");
            } else {
                return Result.errorTyped(404, "配置分组不存在");
            }
        } catch (Exception e) {
            log.error("删除用户AI配置分组失败", e);
            return Result.errorTyped(500, "删除失败: " + e.getMessage());
        }
    }

    // ========================
    // API密钥管理
    // ========================

    /**
     * 获取用户的所有API密钥
     */
    @GetMapping("/api-keys")
    @Operation(summary = "获取API密钥列表", description = "获取当前用户的所有API密钥")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<List<ApiKeyDto>> getUserApiKeys(Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            List<UserApiKey> apiKeys = aiConfigService.getUserApiKeys(userId);
            List<ApiKeyDto> dtos = apiKeys.stream()
                    .map(this::convertToApiKeyDto)
                    .collect(Collectors.toList());

            log.debug("获取用户API密钥列表成功 - 用户ID: {}, 数量: {}", userId, dtos.size());
            return Result.success("获取成功", dtos);
        } catch (Exception e) {
            log.error("获取用户API密钥列表失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据配置分组ID获取API密钥
     */
    @GetMapping("/api-keys/config-group/{configGroupId}")
    @Operation(summary = "根据配置分组ID获取API密钥", description = "获取指定配置分组的API密钥列表")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<List<ApiKeyDto>> getUserApiKeysByConfigGroupId(
            @PathVariable Long configGroupId,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            List<UserApiKey> apiKeys = aiConfigService.getUserApiKeysByConfigGroupId(userId, configGroupId);
            List<ApiKeyDto> dtos = apiKeys.stream()
                    .map(this::convertToApiKeyDto)
                    .collect(Collectors.toList());

            log.debug("获取用户指定配置分组API密钥成功 - 用户ID: {}, 配置分组ID: {}, 数量: {}",
                    userId, configGroupId, dtos.size());
            return Result.success("获取成功", dtos);
        } catch (Exception e) {
            log.error("获取用户指定配置分组API密钥失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 批量添加API密钥
     */
    @PostMapping("/api-keys/batch")
    @Operation(summary = "批量添加API密钥", description = "为当前用户批量添加API密钥并自动测试")
    @ApiResponse(responseCode = "200", description = "批量添加完成")
    public Result<BatchAddApiKeyResult> batchAddApiKeys(
            @Valid @RequestBody BatchAddApiKeyRequest request,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);

            BatchAddApiKeyResult result = aiConfigService.batchAddApiKeys(userId, request);

            log.info("批量添加API密钥完成 - 用户ID: {}, 配置分组ID: {}, 总数: {}, 成功: {}, 失败: {}",
                    userId, request.getConfigGroupId(), result.getTotalCount(),
                    result.getSuccessCount(), result.getFailedCount());
            return Result.success("批量添加完成", result);
        } catch (Exception e) {
            log.error("批量添加API密钥失败", e);
            return Result.errorTyped(500, "批量添加失败: " + e.getMessage());
        }
    }





    /**
     * 获取API密钥负载统计
     */
    @GetMapping("/api-keys/{keyId}/stats")
    @Operation(summary = "获取API密钥负载统计", description = "获取指定API密钥的负载统计信息")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<LoadBalancerService.LoadBalanceStats> getApiKeyStats(
            @PathVariable @NotNull Long keyId,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            // 这里应该验证用户权限，简化处理
            LoadBalancerService.LoadBalanceStats stats = loadBalancerService.getLoadBalanceStats(keyId);

            if (stats != null) {
                log.debug("获取API密钥负载统计成功 - 用户ID: {}, 密钥ID: {}", userId, keyId);
                return Result.success("获取成功", stats);
            } else {
                return Result.errorTyped(404, "统计信息不存在");
            }
        } catch (Exception e) {
            log.error("获取API密钥负载统计失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    // ========================
    // 私有辅助方法
    // ========================

    /**
     * 从认证信息中获取用户ID
     */
    private Long getUserId(Authentication authentication) {
        if (authentication == null) {
            throw new RuntimeException("用户未认证");
        }

        try {
            CustomUserDetails userDetails=(CustomUserDetails)authentication.getPrincipal();
            return userDetails.getUserId();
        } catch (NumberFormatException e) {
            throw new RuntimeException("无效的用户ID");
        }
    }

    /**
     * 转换为用户配置分组DTO
     */
    private UserConfigGroupDto convertToUserConfigGroupDto(UserAiConfigGroups config) {
        UserConfigGroupDto dto = new UserConfigGroupDto();
        // 注意：不设置ID，避免前端直接操作数据库主键
        dto.setGroupName(config.getGroupName());
        dto.setProvider(config.getProvider());
        dto.setCustomBaseUrl(config.getCustomBaseUrl());
        dto.setTestModel(config.getTestModel());
        dto.setPreferredModel(config.getPreferredModel());
        dto.setDefaultTemperature(config.getDefaultTemperature());
        dto.setDefaultMaxTokens(config.getDefaultMaxTokens());
        dto.setDefaultTopP(config.getDefaultTopP());
        dto.setStreamEnabled(config.getStreamEnabled());
        dto.setTimeoutSeconds(config.getTimeoutSeconds());
        dto.setSystemPrompt(config.getSystemPrompt());
        // 添加时间信息，前端可用于显示
        dto.setCreatedAt(config.getCreatedAt());
        dto.setUpdatedAt(config.getUpdatedAt());
        return dto;
    }

    /**
     * 转换为用户配置实体
     */
    private UserAiConfigGroups convertToUserAiConfig(UserConfigGroupDto dto) {
        UserAiConfigGroups config = new UserAiConfigGroups();
        // 注意：不设置ID，让数据库自动生成或由业务逻辑处理
        config.setGroupName(dto.getGroupName());
        config.setProvider(dto.getProvider());
        config.setCustomBaseUrl(dto.getCustomBaseUrl());
        config.setTestModel(dto.getTestModel());
        config.setPreferredModel(dto.getPreferredModel());
        config.setDefaultTemperature(dto.getDefaultTemperature());
        config.setDefaultMaxTokens(dto.getDefaultMaxTokens());
        config.setDefaultTopP(dto.getDefaultTopP());
        config.setStreamEnabled(dto.getStreamEnabled());
        config.setTimeoutSeconds(dto.getTimeoutSeconds());
        config.setSystemPrompt(dto.getSystemPrompt());
        // 注意：时间字段由数据库自动设置，不从DTO获取
        return config;
    }

    /**
     * 转换为API密钥DTO
     */
    private ApiKeyDto convertToApiKeyDto(UserApiKey apiKey) {
        ApiKeyDto dto = new ApiKeyDto();
        dto.setId(apiKey.getId());
        dto.setConfigGroupId(apiKey.getConfigGroupId());
        dto.setMaskedApiKey(apiKey.getApiKeyEncrypted()); // 已经脱敏
        dto.setIsActive(apiKey.getIsActive());
        dto.setPriority(apiKey.getPriority());
        dto.setUsageCount(apiKey.getUsageCount());
        dto.setLastUsedAt(apiKey.getLastUsedAt());
        dto.setCreatedAt(apiKey.getCreatedAt());

        // 获取负载统计信息
        try {
            LoadBalancerService.LoadBalanceStats stats = loadBalancerService.getLoadBalanceStats(apiKey.getId());
            if (stats != null) {
                dto.setIsHealthy(stats.getIsHealthy());
                dto.setCurrentRequests(stats.getCurrentRequests());
                dto.setErrorCount(stats.getErrorCount());
                dto.setErrorRate(stats.getErrorRate());
            }
        } catch (Exception e) {
            log.warn("获取API密钥负载统计失败 - ID: {}", apiKey.getId(), e);
        }

        return dto;
    }

    /**
     * 生成OpenAI格式的兼容API密钥（使用服务类）
     */
    @PostMapping("/generate-compatible-key")
    @Operation(summary = "生成兼容API密钥", description = "为当前用户生成OpenAI格式的兼容API密钥")
    @ApiResponse(responseCode = "200", description = "生成成功")
    public Result<CompatibleKeyResult> generateCompatibleKey(
            @RequestParam(required = false, defaultValue = "default") String keyName,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);

            // 使用服务类创建兼容密钥
            CreateCompatibleKeyRequest request = new CreateCompatibleKeyRequest();
            request.setKeyName(keyName);

            CompatibleApiKeyDto ApiKey = compatibleApiKeyService.createCompatibleKey(userId, request);

            CompatibleKeyResult result = new CompatibleKeyResult(
                    ApiKey.getCompatibleKey(),
                "兼容API密钥已生成并保存，可用于所有AI服务调用",
                    ApiKey.getKeyHash(),
                    ApiKey.getId()
            );

            log.info("生成兼容API密钥成功 - 用户ID: {}, 密钥名称: {}, 密钥ID: {}", userId, keyName, ApiKey.getId());
            return Result.success("生成成功", result);
        } catch (Exception e) {
            log.error("生成兼容API密钥失败", e);
            return Result.errorTyped(500, "生成失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的兼容API密钥列表
     */
    @GetMapping("/compatible-keys")
    @Operation(summary = "获取兼容API密钥列表", description = "获取当前用户的所有兼容API密钥")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<List<CompatibleApiKeyDto>> getUserCompatibleKeys(Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            List<CompatibleApiKeyDto> result = compatibleApiKeyService.getUserCompatibleKeys(userId);

            log.debug("获取用户兼容API密钥列表成功 - 用户ID: {}, 数量: {}", userId, result.size());
            return Result.success("获取成功", result);
        } catch (Exception e) {
            log.error("获取用户兼容API密钥列表失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 删除兼容API密钥
     */
    @DeleteMapping("/compatible-keys/{keyId}")
    @Operation(summary = "删除兼容API密钥", description = "删除指定的兼容API密钥")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public Result<String> deleteCompatibleKey(
            @PathVariable @NotNull Long keyId,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            compatibleApiKeyService.deleteCompatibleKey(userId, keyId);

            log.info("删除兼容API密钥成功 - 用户ID: {}, 密钥ID: {}", userId, keyId);
            return Result.success("删除成功", "删除成功");
        } catch (Exception e) {
            log.error("删除兼容API密钥失败", e);
            return Result.errorTyped(500, "删除失败: " + e.getMessage());
        }
    }

    /**
     * 验证兼容API密钥（使用服务类）
     */
    @PostMapping("/validate-compatible-key")
    @Operation(summary = "验证兼容API密钥", description = "验证兼容API密钥的有效性")
    @ApiResponse(responseCode = "200", description = "验证完成")
    public Result<CompatibleKeyValidation> validateCompatibleKey(
            @RequestParam String compatibleKey,
            Authentication authentication) {
        try {
            boolean isValid = compatibleApiKeyService.validateCompatibleKey(compatibleKey);

            CompatibleKeyValidation result = new CompatibleKeyValidation(
                isValid,
                null, // 不返回用户ID信息
                isValid ? "验证成功" : "验证失败"
            );

            log.debug("验证兼容API密钥 - 有效: {}", isValid);
            return Result.success("验证完成", result);
        } catch (Exception e) {
            log.error("验证兼容API密钥失败", e);
            return Result.success("验证完成", new CompatibleKeyValidation(false, null, "验证失败: " + e.getMessage()));
        }
    }



    /**
     * 兼容密钥生成结果
     */
    public static class CompatibleKeyResult {
        private final String compatibleKey;
        private final String message;
        private final String keyHash;
        private final Long keyId;

        public CompatibleKeyResult(String compatibleKey, String message, String keyHash, Long keyId) {
            this.compatibleKey = compatibleKey;
            this.message = message;
            this.keyHash = keyHash;
            this.keyId = keyId;
        }

        public String getCompatibleKey() { return compatibleKey; }
        public String getMessage() { return message; }
        public String getKeyHash() { return keyHash; }
        public Long getKeyId() { return keyId; }
    }

    /**
     * 兼容密钥验证结果
     */
    public static class CompatibleKeyValidation {
        private final boolean valid;
        private final Long userId;
        private final String message;

        public CompatibleKeyValidation(boolean valid, Long userId, String message) {
            this.valid = valid;
            this.userId = userId;
            this.message = message;
        }

        public boolean isValid() { return valid; }
        public Long getUserId() { return userId; }
        public String getMessage() { return message; }
    }


}
