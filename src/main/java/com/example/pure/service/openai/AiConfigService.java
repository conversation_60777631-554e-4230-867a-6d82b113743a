package com.example.pure.service.openai;


import com.example.pure.model.dto.request.openai.BatchAddApiKeyRequest;
import com.example.pure.model.dto.response.openai.ApiKeyTestResult;
import com.example.pure.model.dto.response.openai.BatchAddApiKeyResult;
import com.example.pure.model.dto.response.openai.UserConfigGroupDto;
import com.example.pure.model.entity.UserAiConfigGroups;
import com.example.pure.model.entity.UserApiKey;

import java.util.List;

/**
 * AI配置管理服务接口
 * <p>
 * 提供用户AI配置分组和API密钥的管理功能
 * </p>
 */
public interface AiConfigService {

    // ========================
    // 用户AI配置分组管理
    // ========================

    /**
     * 获取用户所有AI配置分组
     *
     * @param userId 用户ID
     * @return 用户AI配置分组列表
     */
    List<UserAiConfigGroups> getUserConfigGroups(Long userId);

    /**
     * 获取用户指定分组的AI配置
     *
     * @param userId 用户ID
     * @param groupName 分组名称
     * @return 用户AI配置，如果不存在则返回null
     */
    UserAiConfigGroups getUserConfigByGroup(Long userId, String groupName);

    /**
     * 创建或更新用户AI配置分组
     *
     * @param userId 用户ID
     * @param config 配置信息
     * @return 更新后的配置
     */
    UserAiConfigGroups createOrUpdateUserConfigGroup(Long userId, UserAiConfigGroups config);

    /**
     * 删除用户AI配置分组
     *
     * @param userId 用户ID
     * @param groupName 分组名称
     * @return 是否删除成功
     */
    boolean deleteUserConfigGroup(Long userId, String groupName);

    // ========================
    // DTO查询方法（性能优化）
    // ========================

    /**
     * 直接获取用户配置分组DTO列表（性能优化）
     * <p>
     * 避免Entity→DTO转换，直接返回前端需要的数据格式
     * </p>
     *
     * @param userId 用户ID
     * @return 用户AI配置分组DTO列表
     */
    List<UserConfigGroupDto> getUserConfigGroupDtos(Long userId);

    /**
     * 直接获取指定分组的配置DTO（性能优化）
     *
     * @param userId 用户ID
     * @param groupName 分组名称
     * @return 用户AI配置分组DTO
     */
    UserConfigGroupDto getUserConfigGroupDto(Long userId, String groupName);

    // ========================
    // API密钥管理
    // ========================

    /**
     * 获取用户的所有API密钥
     *
     * @param userId 用户ID
     * @return API密钥列表（已脱敏）
     */
    List<UserApiKey> getUserApiKeys(Long userId);

    /**
     * 根据配置分组ID获取用户的API密钥
     *
     * @param userId   用户ID
     * @param configGroupId 配置分组ID
     * @return API密钥列表
     */
    List<UserApiKey> getUserApiKeysByConfigGroupId(Long userId, Long configGroupId);

    /**
     * 批量添加API密钥并测试有效性
     *
     * @param userId    用户ID
     * @param request   批量添加请求
     * @return 批量添加结果
     */
    BatchAddApiKeyResult batchAddApiKeys(Long userId, BatchAddApiKeyRequest request);

    /**
     * 测试API密钥
     *
     * @param userId 用户ID
     * @param keyId API密钥ID
     * @return 测试结果
     */
    ApiKeyTestResult testApiKey(Long userId, Long keyId);

    /**
     * 更新API密钥
     *
     * @param userId  用户ID
     * @param keyId   密钥ID
     * @param apiKey  原始API密钥（可选，为空则不更新）
     * @param priority 优先级
     * @param isActive 是否激活
     * @return 更新后的API密钥信息（已脱敏）
     */
    UserApiKey updateApiKey(Long userId, Long keyId,  String apiKey, Integer priority, Boolean isActive);

    /**
     * 删除API密钥
     *
     * @param userId 用户ID
     * @param keyId  密钥ID
     * @return 是否删除成功
     */
    boolean deleteApiKey(Long userId, Long keyId);



    // ========================
    // 兼容密钥功能已迁移到 CompatibleKeyService
    // ========================

    // ========================
    // 注意：相关的数据传输对象已移动到独立的包中
    // 请参考：com.example.pure.model.config 包
    // ========================
}
