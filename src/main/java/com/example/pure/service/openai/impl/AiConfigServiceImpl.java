package com.example.pure.service.openai.impl;

import com.example.pure.mapper.primary.UserAiConfigMapper;
import com.example.pure.mapper.primary.UserApiKeyMapper;
import com.example.pure.model.adapter.ChatCompletionRequest;
import com.example.pure.model.adapter.ChatCompletionResponse;
import com.example.pure.model.dto.request.openai.BatchAddApiKeyRequest;
import com.example.pure.model.dto.response.openai.ApiKeyDto;
import com.example.pure.model.dto.response.openai.ApiKeyTestResult;
import com.example.pure.model.dto.response.openai.BatchAddApiKeyResult;
import com.example.pure.model.dto.response.openai.UserConfigGroupDto;
import com.example.pure.model.entity.UserAiConfigGroups;
import com.example.pure.model.entity.UserApiKey;
import com.example.pure.service.openai.AiConfigService;
import com.example.pure.service.openai.LoadBalancerService;
import com.example.pure.service.openai.ModelAdapterService;
import com.example.pure.util.SpringEncryptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AI配置管理服务实现类
 * <p>
 * 实现用户AI配置分组和API密钥的管理功能
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiConfigServiceImpl implements AiConfigService {

    private final UserAiConfigMapper userAiConfigMapper;
    private final UserApiKeyMapper userApiKeyMapper;
    private final LoadBalancerService loadBalancerService;
    private final ModelAdapterService modelAdapterService;
    private final SpringEncryptionUtil encryptionUtil;

    // ========================
    // 用户AI配置分组管理
    // ========================

    @Override
    public List<UserAiConfigGroups> getUserConfigGroups(Long userId) {
        log.debug("获取用户AI配置分组列表 - 用户ID: {}", userId);
        return userAiConfigMapper.selectByUserId(userId);
    }

    @Override
    public UserAiConfigGroups getUserConfigByGroup(Long userId, String groupName) {
        log.debug("获取用户AI配置分组 - 用户ID: {}, 分组: {}", userId, groupName);
        return userAiConfigMapper.selectByUserIdAndGroupName(userId, groupName);
    }

    @Override
    @Transactional
    public UserAiConfigGroups createOrUpdateUserConfigGroup(Long userId, UserAiConfigGroups config) {
        log.info("创建或更新用户AI配置分组 - 用户ID: {}, 分组: {}", userId, config.getGroupName());

        config.setUserId(userId);

        // 检查分组是否已存在
        if (userAiConfigMapper.existsByUserIdAndGroupName(userId, config.getGroupName())) {
            // 更新现有配置
            int updated = userAiConfigMapper.updateByUserIdAndGroupName(config);
            if (updated == 0) {
                throw new RuntimeException("更新用户AI配置分组失败");
            }
        } else {
            // 插入新配置
            int inserted = userAiConfigMapper.insert(config);
            if (inserted == 0) {
                throw new RuntimeException("创建用户AI配置分组失败");
            }
        }

        return getUserConfigByGroup(userId, config.getGroupName());
    }

    @Override
    @Transactional
    public boolean deleteUserConfigGroup(Long userId, String groupName) {
        log.info("删除用户AI配置分组 - 用户ID: {}, 分组: {}", userId, groupName);

        // 获取配置分组ID
        UserAiConfigGroups config = getUserConfigByGroup(userId, groupName);
        if (config == null) {
            return false;
        }

        // 先删除该分组下的所有API密钥
        userApiKeyMapper.deleteByUserIdAndConfigGroupId(userId, config.getId());

        // 删除配置分组
        int deleted = userAiConfigMapper.deleteByUserIdAndGroupName(userId, groupName);
        return deleted > 0;
    }

    // ========================
    // DTO查询方法（性能优化）
    // ========================

    @Override
    public List<UserConfigGroupDto> getUserConfigGroupDtos(Long userId) {
        log.debug("直接获取用户AI配置分组DTO列表 - 用户ID: {}", userId);
        return userAiConfigMapper.selectConfigGroupDtosByUserId(userId);
    }

    @Override
    public UserConfigGroupDto getUserConfigGroupDto(Long userId, String groupName) {
        log.debug("直接获取用户AI配置分组DTO - 用户ID: {}, 分组: {}", userId, groupName);
        return userAiConfigMapper.selectConfigGroupDtoByUserIdAndGroupName(userId, groupName);
    }

    // ========================
    // API密钥管理
    // ========================

    @Override
    public List<UserApiKey> getUserApiKeys(Long userId) {
        log.debug("获取用户API密钥列表 - 用户ID: {}", userId);

        List<UserApiKey> apiKeys = userApiKeyMapper.selectByUserId(userId);

        // 脱敏处理
        return apiKeys.stream()
                .peek(this::maskApiKeyForDisplay)
                .collect(Collectors.toList());
    }

    @Override
    public List<UserApiKey> getUserApiKeysByConfigGroupId(Long userId, Long configGroupId) {
        log.debug("获取用户指定配置分组的API密钥 - 用户ID: {}, 配置分组ID: {}", userId, configGroupId);

        List<UserApiKey> apiKeys = userApiKeyMapper.selectByUserIdAndConfigGroupId(userId, configGroupId);

        // 脱敏处理
        return apiKeys.stream()
                .peek(this::maskApiKeyForDisplay)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public BatchAddApiKeyResult batchAddApiKeys(Long userId, BatchAddApiKeyRequest request) {
        log.info("批量添加API密钥 - 用户ID: {}, 配置分组ID: {}, 数量: {}",
                userId, request.getConfigGroupId(), request.getApiKeys().size());

        // 获取分组配置
        List<UserAiConfigGroups> configs = userAiConfigMapper.selectByUserId(userId);
        UserAiConfigGroups configGroup = configs.stream()
                .filter(config -> config.getId().equals(request.getConfigGroupId()))
                .findFirst()
                .orElse(null);
        if (configGroup == null) {
            throw new RuntimeException("配置分组不存在: " + request.getConfigGroupId());
        }

        List<ApiKeyDto> successKeys = new ArrayList<>();
        List<BatchAddApiKeyResult.FailedApiKey> failedKeys = new ArrayList<>();

        // 🚀 支持并行测试API密钥（可选优化）
        for (int i = 0; i < request.getApiKeys().size(); i++) {
            String apiKey = request.getApiKeys().get(i);
            String maskedKey = maskApiKey(apiKey);

            try {
                // 测试API密钥有效性
                boolean isValid = testApiKeyWithModel(configGroup.getProvider(), apiKey, configGroup.getTestModel(), configGroup.getCustomBaseUrl());

                if (isValid) {
                    // 加密API密钥
                    String encryptedApiKey = encryptionUtil.encrypt(apiKey);

                    // 创建API密钥实体
                    UserApiKey userApiKey = new UserApiKey()
                            .setUserId(userId)
                            .setConfigGroupId(request.getConfigGroupId())
                            .setApiKeyEncrypted(encryptedApiKey)
                            .setIsActive(request.getIsActive() != null ? request.getIsActive() : true)
                            .setPriority(request.getPriority() != null ? request.getPriority() : 1)
                            .setUsageCount(0L);

                    // 插入数据库
                    int inserted = userApiKeyMapper.insert(userApiKey);
                    if (inserted > 0) {
                        // 初始化负载均衡状态
                        loadBalancerService.initializeLoadBalanceState(userApiKey);

                        // 转换为DTO
                        ApiKeyDto dto = convertToApiKeyDto(userApiKey);
                        successKeys.add(dto);

                        log.info("API密钥添加成功 - 用户ID: {}, 配置分组ID: {}, 密钥: {}",
                                userId, request.getConfigGroupId(), maskedKey);
                    } else {
                        failedKeys.add(new BatchAddApiKeyResult.FailedApiKey(maskedKey, "数据库插入失败"));
                    }
                } else {
                    failedKeys.add(new BatchAddApiKeyResult.FailedApiKey(maskedKey, "API密钥测试失败"));
                }
            } catch (Exception e) {
                log.warn("API密钥处理失败 - 密钥: {}, 错误: {}", maskedKey, e.getMessage());
                failedKeys.add(new BatchAddApiKeyResult.FailedApiKey(maskedKey, "处理异常: " + e.getMessage()));
            }
        }

        // 构建结果
        BatchAddApiKeyResult result = new BatchAddApiKeyResult();
        result.setSuccessKeys(successKeys);
        result.setFailedKeys(failedKeys);
        result.setTotalCount(request.getApiKeys().size());
        result.setSuccessCount(successKeys.size());
        result.setFailedCount(failedKeys.size());

        log.info("批量添加API密钥完成 - 用户ID: {}, 配置分组ID: {}, 总数: {}, 成功: {}, 失败: {}",
                userId, request.getConfigGroupId(), result.getTotalCount(), result.getSuccessCount(), result.getFailedCount());

        return result;
    }

    @Override
    public ApiKeyTestResult testApiKey(Long userId, Long keyId) {
        log.info("测试API密钥 - 用户ID: {}, 密钥ID: {}", userId, keyId);

        // 查询API密钥
        UserApiKey apiKey = userApiKeyMapper.selectById(keyId);
        if (apiKey == null || !apiKey.getUserId().equals(userId)) {
            return new ApiKeyTestResult(false, "API密钥不存在或无权限访问", 0L, null, null);
        }

        // 获取配置分组信息
        List<UserAiConfigGroups> configs = userAiConfigMapper.selectByUserId(userId);
        UserAiConfigGroups configGroup = configs.stream()
                .filter(config -> config.getId().equals(apiKey.getConfigGroupId()))
                .findFirst()
                .orElse(null);

        if (configGroup == null) {
            return new ApiKeyTestResult(false, "配置分组不存在", 0L, null, null);
        }

        try {
            // 解密API密钥
            String decryptedKey = encryptionUtil.decrypt(apiKey.getApiKeyEncrypted());

            // 测试API密钥有效性
            long startTime = System.currentTimeMillis();
            boolean isValid = testApiKeyWithModel(configGroup.getProvider(), decryptedKey,
                    configGroup.getTestModel(), configGroup.getCustomBaseUrl());
            long responseTime = System.currentTimeMillis() - startTime;

            if (isValid) {
                return new ApiKeyTestResult(true, "API密钥有效，连接正常", responseTime,
                        configGroup.getProvider(), null);
            } else {
                return new ApiKeyTestResult(false, "API密钥无效", responseTime, null, null);
            }
        } catch (Exception e) {
            log.error("测试API密钥失败", e);
            return new ApiKeyTestResult(false, "测试失败: " + e.getMessage(), 0L, null, null);
        }
    }

    @Override
    @Transactional
    public boolean deleteApiKey(Long userId, Long keyId) {
        log.info("删除API密钥 - 用户ID: {}, 密钥ID: {}", userId, keyId);

        // 查询现有密钥
        UserApiKey existingKey = userApiKeyMapper.selectById(keyId);
        if (existingKey == null || !existingKey.getUserId().equals(userId)) {
            throw new RuntimeException("API密钥不存在或无权限访问");
        }

        // 清理负载均衡状态
        loadBalancerService.cleanupLoadBalanceState(keyId);

        // 删除密钥
        int deleted = userApiKeyMapper.deleteById(keyId);

        log.info("API密钥删除成功 - ID: {}", keyId);
        return deleted > 0;
    }

    @Override
    @Transactional
    public UserApiKey updateApiKey(Long userId, Long keyId, String apiKey, Integer priority, Boolean isActive) {
        log.info("更新API密钥 - 用户ID: {}, 密钥ID: {}", userId, keyId);

        // 查询现有密钥
        UserApiKey existingKey = userApiKeyMapper.selectById(keyId);
        if (existingKey == null || !existingKey.getUserId().equals(userId)) {
            throw new RuntimeException("API密钥不存在或无权限访问");
        }

        // 更新字段
        if (StringUtils.hasText(apiKey)) {
            existingKey.setApiKeyEncrypted(encryptionUtil.encrypt(apiKey));
        }
        if (priority != null) {
            existingKey.setPriority(priority);
        }
        if (isActive != null) {
            existingKey.setIsActive(isActive);
        }

        // 更新数据库
        int updated = userApiKeyMapper.updateById(existingKey);
        if (updated == 0) {
            throw new RuntimeException("更新API密钥失败");
        }

        // 脱敏处理后返回
        maskApiKeyForDisplay(existingKey);

        log.info("API密钥更新成功 - ID: {}", keyId);
        return existingKey;
    }

    // ========================
    // 私有辅助方法
    // ========================

    /**
     * 测试API密钥有效性（使用实际模型调用）
     * <p>
     * 通过发起真实的聊天请求来验证API密钥和模型的可用性
     * 这比仅仅调用/models端点更准确
     * </p>
     */
    private boolean testApiKeyWithModel(String provider, String apiKey, String testModel, String customBaseUrl) {
        try {
            // 根据提供商类型转换
            UserApiKey.ProviderType providerType = UserApiKey.ProviderType.valueOf(provider.toUpperCase());

            // 🚀 改进：使用实际的聊天请求测试API密钥和模型
            return testApiKeyWithActualRequest(providerType, apiKey, testModel, customBaseUrl);

        } catch (Exception e) {
            log.warn("测试API密钥失败 - 提供商: {}, 模型: {}, 错误: {}", provider, testModel, e.getMessage());
            return false;
        }
    }

    /**
     * 使用实际聊天请求测试API密钥
     * <p>
     * 发送一个简单的测试消息来验证API密钥和模型的真实可用性
     * </p>
     */
    private boolean testApiKeyWithActualRequest(UserApiKey.ProviderType provider, String apiKey, String testModel, String customBaseUrl) {
        try {
            // 构建测试请求
            ChatCompletionRequest testRequest = createTestChatRequest(testModel);

            // 🎯 关键改进：使用实际的聊天完成请求进行测试
            ChatCompletionResponse response = modelAdapterService.chatCompletion(provider, apiKey, testRequest)
                    .timeout(Duration.ofSeconds(10))  // 设置较短的超时时间
                    .block();

            // 验证响应是否有效
            boolean isValid = response != null &&
                             response.getChoices() != null &&
                             !response.getChoices().isEmpty() &&
                             response.getChoices().get(0).getMessage() != null;

            if (isValid) {
                Object content = response.getChoices().get(0).getMessage().getContent();
                String contentStr = content != null ? content.toString() : "";
                log.debug("API密钥测试成功 - 提供商: {}, 模型: {}, 响应长度: {}",
                         provider, testModel, contentStr.length());
            } else {
                log.warn("API密钥测试失败 - 提供商: {}, 模型: {}, 响应无效", provider, testModel);
            }

            return isValid;

        } catch (Exception e) {
            log.warn("API密钥实际请求测试失败 - 提供商: {}, 模型: {}, 错误: {}", provider, testModel, e.getMessage());
            return false;
        }
    }

    /**
     * 创建测试用的聊天请求
     * <p>
     * 创建一个简单、轻量的测试请求，用于验证API密钥和模型可用性
     * </p>
     */
    private ChatCompletionRequest createTestChatRequest(String model) {
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setModel(model != null ? model : "gpt-3.5-turbo");  // 使用配置的测试模型
        request.setMaxTokens(10);  // 限制token数量，减少费用
        request.setTemperature(0.1);  // 低温度，确保稳定输出
        request.setStream(false);  // 非流式，简化处理

        // 创建简单的测试消息
        ChatCompletionRequest.ChatMessage testMessage = new ChatCompletionRequest.ChatMessage();
        testMessage.setRole("user");
        testMessage.setContent("Hi");  // 极简测试内容

        request.setMessages(List.of(testMessage));

        return request;
    }

    /**
     * 脱敏API密钥
     */
    private String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() < 8) {
            return "****";
        }
        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
    }

    /**
     * 转换为ApiKeyDto
     */
    private ApiKeyDto convertToApiKeyDto(UserApiKey apiKey) {
        ApiKeyDto dto = new ApiKeyDto();
        dto.setId(apiKey.getId());
        dto.setConfigGroupId(apiKey.getConfigGroupId());
        dto.setMaskedApiKey(maskApiKey(encryptionUtil.decrypt(apiKey.getApiKeyEncrypted())));
        dto.setIsActive(apiKey.getIsActive());
        dto.setPriority(apiKey.getPriority());
        dto.setUsageCount(apiKey.getUsageCount());
        dto.setLastUsedAt(apiKey.getLastUsedAt());
        dto.setCreatedAt(apiKey.getCreatedAt());
        return dto;
    }

    /**
     * 脱敏处理API密钥用于显示
     */
    private void maskApiKeyForDisplay(UserApiKey apiKey) {
        if (apiKey.getApiKeyEncrypted() != null) {
            try {
                String decryptedKey = encryptionUtil.decrypt(apiKey.getApiKeyEncrypted());
                apiKey.setApiKeyEncrypted(maskApiKey(decryptedKey));
            } catch (Exception e) {
                log.warn("脱敏API密钥失败", e);
                apiKey.setApiKeyEncrypted("****");
            }
        }
    }
}
