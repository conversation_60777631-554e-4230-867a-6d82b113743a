package com.example.pure.service.openai;

import com.example.pure.model.dto.log.RequestLogInfo;
import com.example.pure.model.dto.request.openai.OpenAiChatRequest;
import com.example.pure.model.dto.request.openai.OpenAiImageRequest;
import com.example.pure.model.dto.response.openai.OpenAiImageResponse;
import com.example.pure.model.dto.response.openai.OpenAiModelResponse;

/**
 * OpenAI请求处理服务接口
 * <p>
 * 负责处理OpenAI兼容API的业务逻辑，包括参数验证、日志记录和业务处理
 * </p>
 */
public interface OpenAiRequestService {

    /**
     * 处理聊天完成请求（支持流式和非流式）
     * <p>
     * 根据stream参数自动选择处理方式
     * </p>
     *
     * @param apiKey 认证的API密钥
     * @param request 聊天请求对象
     * @param logInfo 请求日志信息对象
     * @return 流式返回SseEmitter，非流式返回OpenAiChatResponse
     */
    Object processChatCompletions(String apiKey, OpenAiChatRequest request, RequestLogInfo logInfo);

    /**
     * 处理图片生成请求
     *
     * @param apiKey 认证的API密钥
     * @param request 图片生成请求对象
     * @param logInfo 请求日志信息对象
     * @return 图片生成响应
     */
    OpenAiImageResponse processImageGeneration(String apiKey, OpenAiImageRequest request, RequestLogInfo logInfo);

    /**
     * 处理模型列表请求
     *
     * @param apiKey 认证的API密钥
     * @return 模型列表响应
     */
    OpenAiModelResponse processModelList(String apiKey);

    /**
     * 验证请求参数
     *
     * @param request 请求对象
     * @throws IllegalArgumentException 参数验证失败时抛出
     */
    void validateRequest(Object request) throws IllegalArgumentException;
}
