package com.example.pure.service.openai;

import com.example.pure.model.entity.UserApiKey;

/**
 * 负载均衡服务接口
 * <p>
 * 提供智能负载均衡算法，实现API密钥的智能选择和健康管理
 * </p>
 */
public interface LoadBalancerService {

    /**
     * 选择最佳的API密钥（基于提供商）
     * <p>
     * 根据负载均衡算法选择当前最适合的API密钥
     * </p>
     *
     * @param userId   用户ID
     * @param provider 提供商类型
     * @return 选中的API密钥，如果没有可用密钥则返回null
     */
    UserApiKey selectBestApiKey(Long userId, UserApiKey.ProviderType provider);

    /**
     * 选择最佳的API密钥（基于配置分组）
     * <p>
     * 根据负载均衡算法从指定配置分组中选择当前最适合的API密钥
     * </p>
     *
     * @param userId        用户ID
     * @param configGroupId 配置分组ID
     * @return 选中的API密钥，如果没有可用密钥则返回null
     */
    UserApiKey selectBestApiKeyFromGroup(Long userId, Long configGroupId);

    /**
     * 开始使用API密钥
     * <p>
     * 在开始API调用前调用，用于更新负载状态
     * </p>
     *
     * @param apiKeyId API密钥ID
     */
    void startUsingApiKey(Long apiKeyId);

    /**
     * 结束使用API密钥
     * <p>
     * 在API调用结束后调用，用于更新负载状态
     * </p>
     *
     * @param apiKeyId API密钥ID
     * @param success  调用是否成功
     */
    void finishUsingApiKey(Long apiKeyId, boolean success);

    /**
     * 报告API密钥错误
     * <p>
     * 当API调用失败时调用，用于更新错误统计和健康状态
     * </p>
     *
     * @param apiKeyId API密钥ID
     * @param error    错误信息
     */
    void reportApiKeyError(Long apiKeyId, String error);

    /**
     * 检查API密钥健康状态
     * <p>
     * 检查指定API密钥的健康状态
     * </p>
     *
     * @param apiKeyId API密钥ID
     * @return 是否健康
     */
    boolean isApiKeyHealthy(Long apiKeyId);

    /**
     * 更新API密钥健康状态
     * <p>
     * 手动更新API密钥的健康状态
     * </p>
     *
     * @param apiKeyId  API密钥ID
     * @param isHealthy 是否健康
     */
    void updateApiKeyHealth(Long apiKeyId, boolean isHealthy);

    /**
     * 获取API密钥负载统计
     * <p>
     * 获取指定API密钥的负载统计信息
     * </p>
     *
     * @param apiKeyId API密钥ID
     * @return 负载统计信息
     */
    LoadBalanceStats getLoadBalanceStats(Long apiKeyId);

    /**
     * 重置API密钥负载状态
     * <p>
     * 重置指定API密钥的负载状态为初始状态
     * </p>
     *
     * @param apiKeyId API密钥ID
     */
    void resetLoadBalanceState(Long apiKeyId);

    /**
     * 初始化API密钥负载状态
     * <p>
     * 为新添加的API密钥初始化负载状态
     * </p>
     *
     * @param apiKey API密钥信息
     */
    void initializeLoadBalanceState(UserApiKey apiKey);

    /**
     * 清理API密钥负载状态
     * <p>
     * 删除指定API密钥的负载状态记录
     * </p>
     *
     * @param apiKeyId API密钥ID
     */
    void cleanupLoadBalanceState(Long apiKeyId);

    /**
     * 负载均衡统计信息
     */
    class LoadBalanceStats {
        private final Long apiKeyId;
        private final Integer currentRequests;
        private final Long totalRequests;
        private final Integer errorCount;
        private final Boolean isHealthy;
        private final Double errorRate;

        public LoadBalanceStats(Long apiKeyId, Integer currentRequests, Long totalRequests,
                               Integer errorCount, Boolean isHealthy) {
            this.apiKeyId = apiKeyId;
            this.currentRequests = currentRequests;
            this.totalRequests = totalRequests;
            this.errorCount = errorCount;
            this.isHealthy = isHealthy;
            this.errorRate = totalRequests > 0 ? (double) errorCount / totalRequests : 0.0;
        }

        // Getters
        public Long getApiKeyId() { return apiKeyId; }
        public Integer getCurrentRequests() { return currentRequests; }
        public Long getTotalRequests() { return totalRequests; }
        public Integer getErrorCount() { return errorCount; }
        public Boolean getIsHealthy() { return isHealthy; }
        public Double getErrorRate() { return errorRate; }
    }
}
